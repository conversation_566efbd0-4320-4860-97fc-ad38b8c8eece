# 工单数据离群分析功能

## 功能概述

本功能为工单数据分析系统新增了全面的离群数据检测和分析能力，能够自动识别和分析工单数据中的异常情况，帮助管理者发现潜在问题并优化工作流程。

## 主要特性

### 1. 多维度离群检测

#### 1.1 处理时长离群检测
- **算法**: 使用IQR（四分位距）方法
- **检测内容**: 识别处理时间异常长或异常短的工单
- **阈值**: 默认使用1.5倍IQR作为离群边界
- **应用场景**: 发现工作效率异常、复杂度评估错误等问题

#### 1.2 响应时间离群检测
- **算法**: 使用Z-Score方法
- **检测内容**: 识别从创建到接单时间异常的工单
- **阈值**: 默认使用2.5倍标准差作为离群边界
- **应用场景**: 发现响应延迟、资源分配不当等问题

#### 1.3 业务规则离群检测
- **检测内容**: 
  - 时间逻辑错误（如开始时间晚于结束时间）
  - 数据完整性问题（如关键字段缺失）
  - 状态与时间不匹配（如已完成状态但无结束时间）
- **应用场景**: 发现数据质量问题、流程执行异常等

### 2. 离群数据统计分析

#### 2.1 基础统计
- 总离群数据数量
- 离群数据占比
- 按类型分组的离群数据分布
- 按严重程度分组的离群数据分布

#### 2.2 详细统计
- **处理时长离群统计**:
  - 超长/超短处理时长工单数量
  - 平均离群程度
  - 最大/最小处理时长
  
- **响应时间离群统计**:
  - 响应过慢/过快工单数量
  - 平均响应时间
  - 最大响应时间
  
- **业务规则离群统计**:
  - 状态异常工单数量
  - 时间逻辑异常工单数量
  - 数据完整性异常工单数量

### 3. AI智能分析

更新了AI分析提示词模板，增加了离群数据分析的专门章节：
- 离群数据类型分析
- 离群数据分布模式识别
- 离群数据对业务的影响评估
- 针对性的改进建议

## 技术实现

### 核心类结构

```
cn.facilityone.ai.dto
├── OutlierData.java              # 离群数据DTO
├── OutlierStatistics.java        # 离群数据统计DTO
└── WorkOrderAnalysisResult.java  # 扩展了离群数据字段

cn.facilityone.ai.util
└── OutlierDetectionUtil.java     # 离群数据检测工具类

cn.facilityone.ai.service.biz
└── ReportBizService.java         # 集成离群数据分析
```

### 离群数据类型枚举

```java
public enum OutlierType {
    PROCESSING_TIME_OUTLIER("处理时长异常"),
    RESPONSE_TIME_OUTLIER("响应时间异常"),
    BUSINESS_RULE_OUTLIER("业务规则异常"),
    DATA_QUALITY_OUTLIER("数据质量异常"),
    FREQUENCY_OUTLIER("频率异常"),
    TIME_PATTERN_OUTLIER("时间模式异常");
}
```

### 严重程度分类

- **严重** (≥3.0): 需要立即关注的异常
- **中等** (2.0-3.0): 需要重点关注的异常
- **轻微** (1.0-2.0): 需要监控的异常
- **极轻微** (<1.0): 轻度异常

## 使用示例

### API响应示例

```json
{
  "statisticalData": {
    "totalCount": 1000,
    "outlierWorkOrders": [...],
    "outlierStatistics": {
      "totalOutlierCount": 45,
      "outlierPercentage": 4.5,
      "outlierTypeDistribution": {
        "PROCESSING_TIME_OUTLIER": 20,
        "RESPONSE_TIME_OUTLIER": 15,
        "BUSINESS_RULE_OUTLIER": 10
      },
      "severityDistribution": {
        "严重": 5,
        "中等": 15,
        "轻微": 20,
        "极轻微": 5
      }
    },
    "processingTimeOutlierCount": 20,
    "responseTimeOutlierCount": 15,
    "businessRuleOutlierCount": 10,
    "totalOutlierCount": 45,
    "outlierPercentage": 4.5
  },
  "aiAnalysis": "基于离群数据分析的AI报告..."
}
```

## 业务价值

### 1. 问题发现
- **及早发现异常**: 自动识别数据中的异常模式
- **质量监控**: 持续监控数据质量和业务流程执行情况
- **风险预警**: 提前发现可能影响业务的问题

### 2. 流程优化
- **效率提升**: 识别处理效率异常，优化资源配置
- **流程改进**: 发现业务流程中的瓶颈和问题点
- **标准化**: 通过异常分析建立更好的业务标准

### 3. 决策支持
- **数据驱动**: 基于客观数据进行管理决策
- **优先级排序**: 根据异常严重程度确定处理优先级
- **趋势分析**: 通过离群数据变化趋势预测业务发展

## 配置参数

### 可调整的检测参数

```java
// IQR方法的倍数（默认1.5）
private static final double DEFAULT_IQR_MULTIPLIER = 1.5;

// Z-Score方法的阈值（默认2.5）
private static final double DEFAULT_Z_SCORE_THRESHOLD = 2.5;
```

### 业务规则配置

- 时间差异容忍度：30分钟
- 必需字段检查：工单号、申请人
- 状态一致性检查：状态与时间字段的匹配规则

## 测试覆盖

### 单元测试
- `OutlierDetectionUtilTest`: 离群检测算法测试
- `ReportBizServiceTest`: 集成测试

### 测试场景
- 正常数据处理
- 各种类型的离群数据检测
- 边界条件处理
- 异常情况处理

## 性能考虑

### 算法复杂度
- IQR方法: O(n log n) - 需要排序
- Z-Score方法: O(n) - 线性时间
- 业务规则检查: O(n) - 线性时间

### 优化策略
- 数据量过大时可考虑采样分析
- 异常处理确保不影响主要分析流程
- 缓存统计结果避免重复计算

## Token优化方案

### 问题背景
在实际使用中发现，当工单数据量较大时，传入AI模型的JSON数据会超出token限制，导致分析失败。

### 解决方案

#### 1. 智能数据压缩
- **自动检测**: 系统自动检测数据大小，当超过阈值时启用压缩
- **多级压缩**: 提供完整版、精简版、极简版三个级别的数据格式
- **压缩效果**: 实测可减少约77%的数据大小

#### 2. 精简版数据结构
```java
CompactWorkOrderAnalysisResult {
    // 只保留AI分析必需的核心统计数据
    // 分布数据只保留前几名（如前5个状态、前3个优先级）
    // 离群数据只包含统计摘要，不包含具体工单对象
    // 包含关键指标摘要，便于快速评估
}
```

#### 3. Token限制配置
- **完整数据限制**: 4000 tokens
- **精简数据限制**: 2000 tokens
- **自动降级**: 超出限制时自动使用更精简的格式

#### 4. 优化效果
- **压缩前**: 21518字符，5379 tokens
- **压缩后**: 1996字符，499 tokens
- **压缩率**: 约90%的token减少

### 使用方式
系统会自动根据数据大小选择合适的格式，无需手动配置：

```java
// 自动压缩
Object compressedData = DataCompressionUtil.compressAnalysisResult(fullResult);

// 获取压缩信息
String sizeInfo = DataCompressionUtil.getDataSizeInfo(compressedData);
```

## 未来扩展

### 1. 机器学习增强
- 使用机器学习算法提高检测精度
- 自适应阈值调整
- 异常模式学习

### 2. 实时监控
- 实时离群数据检测
- 异常告警机制
- 趋势预测

### 3. 可视化增强
- 离群数据分布图表
- 异常趋势可视化
- 交互式异常探索界面

### 4. 进一步优化
- 基于实际使用情况调整压缩策略
- 支持用户自定义压缩级别
- 增加更多智能压缩算法
