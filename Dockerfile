# 基础镜像
# 使用标准 Eclipse Temurin JRE 镜像
FROM docker.1ms.run/eclipse-temurin:21-jre

# SpringBoot 项目通常在运行时向 /tmp 写入临时文件
# 这里的 /tmp 目录就会在运行时自动挂载为匿名卷，任何向 /tmp 中写入的信息都不会记录进容器存储层
VOLUME /tmp
# 外部配置目录
VOLUME /config

# 将构建好的 JAR 包复制到容器中的 /app 目录
WORKDIR /app
COPY target/AI-0.0.1-SNAPSHOT.jar /app/app.jar

# 暴露的端口
EXPOSE 8080

# 解决时间不正确的问题
ENV TZ='Asia/Shanghai'

# 定义容器启动时执行的命令
ENTRYPOINT ["java", "-jar", "app.jar", "--spring.config.location=/config/application.yml"]
