package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.CompactWorkOrderAnalysisResult;
import cn.facilityone.ai.dto.OutlierData;
import cn.facilityone.ai.dto.OutlierStatistics;
import cn.facilityone.ai.dto.WorkOrderAnalysisResult;
import cn.hutool.json.JSONUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据压缩工具类测试
 */
class DataCompressionUtilTest {

    private WorkOrderAnalysisResult largeResult;
    private WorkOrderAnalysisResult smallResult;

    @BeforeEach
    void setUp() {
        largeResult = createLargeAnalysisResult();
        smallResult = createSmallAnalysisResult();
    }

    /**
     * 测试小数据量不需要压缩
     */
    @Test
    void testCompressAnalysisResult_SmallData_NoCompression() {
        Object result = DataCompressionUtil.compressAnalysisResult(smallResult);

        // 小数据应该返回原始对象
        assertSame(smallResult, result);
        assertTrue(result instanceof WorkOrderAnalysisResult);
    }

    /**
     * 测试大数据量自动压缩
     */
    @Test
    void testCompressAnalysisResult_LargeData_Compression() {
        Object result = DataCompressionUtil.compressAnalysisResult(largeResult);

        // 大数据应该返回压缩后的对象
        assertNotSame(largeResult, result);
        assertTrue(result instanceof CompactWorkOrderAnalysisResult);

        CompactWorkOrderAnalysisResult compactResult = (CompactWorkOrderAnalysisResult) result;
        
        // 验证核心数据被保留
        assertEquals(largeResult.getTotalCount(), compactResult.getTotalCount());
        assertEquals(largeResult.getAverageProcessingTime(), compactResult.getAverageProcessingTime());
        assertEquals(largeResult.getCompletionRate(), compactResult.getCompletionRate());
        assertEquals(largeResult.getTotalOutlierCount(), compactResult.getTotalOutlierCount());
        assertEquals(largeResult.getOutlierPercentage(), compactResult.getOutlierPercentage());
    }

    /**
     * 测试压缩后的数据大小确实减少
     */
    @Test
    void testCompressAnalysisResult_DataSizeReduction() {
        String originalJson = JSONUtil.toJsonStr(largeResult);
        Object compressedResult = DataCompressionUtil.compressAnalysisResult(largeResult);
        String compressedJson = JSONUtil.toJsonStr(compressedResult);

        int originalSize = originalJson.length();
        int compressedSize = compressedJson.length();

        assertTrue(compressedSize < originalSize, 
                String.format("压缩后大小(%d)应该小于原始大小(%d)", compressedSize, originalSize));
        
        // 压缩率应该至少达到50%
        double compressionRatio = (double) compressedSize / originalSize;
        assertTrue(compressionRatio < 0.8, 
                String.format("压缩率(%.2f)应该小于0.8", compressionRatio));
    }

    /**
     * 测试token数量估算
     */
    @Test
    void testEstimateTokenCount() {
        String testText = "This is a test string with some content.";
        // 不直接测试私有方法，而是通过公共方法测试
        
        String largeJson = JSONUtil.toJsonStr(largeResult);
        boolean needsCompression = DataCompressionUtil.needsCompression(largeJson);
        
        // 大数据应该需要压缩
        assertTrue(needsCompression);
        
        String smallJson = JSONUtil.toJsonStr(smallResult);
        boolean smallNeedsCompression = DataCompressionUtil.needsCompression(smallJson);
        
        // 小数据可能不需要压缩（取决于具体大小）
        // 这里主要验证方法不会抛出异常
        assertNotNull(smallNeedsCompression);
    }

    /**
     * 测试数据大小信息获取
     */
    @Test
    void testGetDataSizeInfo() {
        String info = DataCompressionUtil.getDataSizeInfo(largeResult);
        assertNotNull(info);
        assertTrue(info.contains("完整版数据"));
        assertTrue(info.contains("字符数"));
        assertTrue(info.contains("token数"));

        Object compressedResult = DataCompressionUtil.compressAnalysisResult(largeResult);
        String compressedInfo = DataCompressionUtil.getDataSizeInfo(compressedResult);
        assertNotNull(compressedInfo);
        assertTrue(compressedInfo.contains("精简版数据"));
    }

    /**
     * 测试空数据处理
     */
    @Test
    void testCompressAnalysisResult_NullData() {
        Object result = DataCompressionUtil.compressAnalysisResult(null);
        assertNull(result);
    }

    /**
     * 测试关键指标摘要生成
     */
    @Test
    void testKeyMetricsSummaryGeneration() {
        Object result = DataCompressionUtil.compressAnalysisResult(largeResult);
        
        if (result instanceof CompactWorkOrderAnalysisResult) {
            CompactWorkOrderAnalysisResult compactResult = (CompactWorkOrderAnalysisResult) result;
            CompactWorkOrderAnalysisResult.KeyMetricsSummary keyMetrics = compactResult.getKeyMetrics();
            
            assertNotNull(keyMetrics);
            assertNotNull(keyMetrics.getEfficiencyLevel());
            assertNotNull(keyMetrics.getDataQualityLevel());
            assertNotNull(keyMetrics.getResponseSpeedLevel());
            assertNotNull(keyMetrics.getPrimaryIssueType());
            assertNotNull(keyMetrics.getImprovementPriority());
        }
    }

    /**
     * 创建大数据量的分析结果（用于测试压缩）
     */
    private WorkOrderAnalysisResult createLargeAnalysisResult() {
        WorkOrderAnalysisResult result = new WorkOrderAnalysisResult();
        
        // 基础数据
        result.setTotalCount(10000);
        result.setAverageProcessingTime(120.5);
        result.setMaxProcessingTime(600);
        result.setMinProcessingTime(10);
        result.setCompletionRate(85.5);
        result.setOnTimeCompletionRate(78.2);
        result.setChargedOrderCount(7000);
        result.setFreeOrderCount(3000);

        // 创建大量分布数据（足够大以触发压缩）
        Map<String, Integer> statusDistribution = new HashMap<>();
        for (int i = 1; i <= 100; i++) {
            statusDistribution.put("状态" + i + "_非常长的状态名称用于增加数据大小_" + i, 500 + i * 10);
        }
        result.setStatusDistribution(statusDistribution);

        Map<String, Integer> departmentDistribution = new HashMap<>();
        for (int i = 1; i <= 200; i++) {
            departmentDistribution.put("部门" + i + "_非常长的部门名称用于增加数据大小_" + i, 200 + i * 5);
        }
        result.setDepartmentDistribution(departmentDistribution);

        Map<String, Integer> serviceTypeDistribution = new HashMap<>();
        for (int i = 1; i <= 150; i++) {
            serviceTypeDistribution.put("服务类型" + i + "_非常长的服务类型名称用于增加数据大小_" + i, 300 + i * 8);
        }
        result.setServiceTypeDistribution(serviceTypeDistribution);

        Map<String, Integer> priorityDistribution = new HashMap<>();
        for (int i = 1; i <= 50; i++) {
            priorityDistribution.put("优先级" + i + "_非常长的优先级名称用于增加数据大小_" + i, 100 + i * 3);
        }
        result.setPriorityDistribution(priorityDistribution);

        Map<String, Integer> buildingDistribution = new HashMap<>();
        for (int i = 1; i <= 80; i++) {
            buildingDistribution.put("大厦" + i + "_非常长的大厦名称用于增加数据大小_" + i, 150 + i * 4);
        }
        result.setBuildingDistribution(buildingDistribution);

        Map<String, Integer> monthlyDistribution = new HashMap<>();
        for (int i = 1; i <= 12; i++) {
            monthlyDistribution.put("月份" + i + "_非常长的月份描述用于增加数据大小_" + i, 800 + i * 20);
        }
        result.setMonthlyDistribution(monthlyDistribution);

        Map<String, Integer> weeklyDistribution = new HashMap<>();
        for (int i = 1; i <= 7; i++) {
            weeklyDistribution.put("星期" + i + "_非常长的星期描述用于增加数据大小_" + i, 1400 + i * 30);
        }
        result.setWeeklyDistribution(weeklyDistribution);

        Map<String, Integer> hourlyDistribution = new HashMap<>();
        for (int i = 0; i <= 23; i++) {
            hourlyDistribution.put("小时" + i + "_非常长的小时描述用于增加数据大小_" + i, 400 + i * 15);
        }
        result.setHourlyDistribution(hourlyDistribution);

        // 离群数据信息
        result.setTotalOutlierCount(450);
        result.setOutlierPercentage(4.5);
        result.setProcessingTimeOutlierCount(200);
        result.setResponseTimeOutlierCount(150);
        result.setBusinessRuleOutlierCount(100);

        // 离群数据统计
        OutlierStatistics outlierStats = new OutlierStatistics();
        outlierStats.setTotalOutlierCount(450);
        outlierStats.setOutlierPercentage(4.5);

        Map<OutlierData.OutlierType, Integer> typeDistribution = new HashMap<>();
        typeDistribution.put(OutlierData.OutlierType.PROCESSING_TIME_OUTLIER, 200);
        typeDistribution.put(OutlierData.OutlierType.RESPONSE_TIME_OUTLIER, 150);
        typeDistribution.put(OutlierData.OutlierType.BUSINESS_RULE_OUTLIER, 100);
        outlierStats.setOutlierTypeDistribution(typeDistribution);

        Map<String, Integer> severityDistribution = new HashMap<>();
        severityDistribution.put("严重", 50);
        severityDistribution.put("中等", 150);
        severityDistribution.put("轻微", 200);
        severityDistribution.put("极轻微", 50);
        outlierStats.setSeverityDistribution(severityDistribution);

        // 处理时长离群统计
        OutlierStatistics.ProcessingTimeOutlierStats processingStats = 
                new OutlierStatistics.ProcessingTimeOutlierStats();
        processingStats.setLongProcessingCount(150);
        processingStats.setShortProcessingCount(50);
        processingStats.setAverageOutlierScore(2.8);
        processingStats.setMaxProcessingTime(1200);
        processingStats.setMinProcessingTime(2);
        outlierStats.setProcessingTimeStats(processingStats);

        // 响应时间离群统计
        OutlierStatistics.ResponseTimeOutlierStats responseStats = 
                new OutlierStatistics.ResponseTimeOutlierStats();
        responseStats.setSlowResponseCount(150);
        responseStats.setFastResponseCount(0);
        responseStats.setAverageResponseTime(180.5);
        responseStats.setMaxResponseTime(720);
        outlierStats.setResponseTimeStats(responseStats);

        // 业务规则离群统计
        OutlierStatistics.BusinessRuleOutlierStats businessStats = 
                new OutlierStatistics.BusinessRuleOutlierStats();
        businessStats.setStatusAnomalyCount(40);
        businessStats.setPriorityAnomalyCount(20);
        businessStats.setTimeLogicAnomalyCount(25);
        businessStats.setDataIntegrityAnomalyCount(15);
        outlierStats.setBusinessRuleStats(businessStats);

        result.setOutlierStatistics(outlierStats);

        return result;
    }

    /**
     * 创建小数据量的分析结果（用于测试不压缩）
     */
    private WorkOrderAnalysisResult createSmallAnalysisResult() {
        WorkOrderAnalysisResult result = new WorkOrderAnalysisResult();
        
        result.setTotalCount(100);
        result.setAverageProcessingTime(90.0);
        result.setCompletionRate(95.0);
        result.setTotalOutlierCount(5);
        result.setOutlierPercentage(5.0);

        // 少量分布数据
        Map<String, Integer> statusDistribution = new HashMap<>();
        statusDistribution.put("已完成", 80);
        statusDistribution.put("进行中", 15);
        statusDistribution.put("待处理", 5);
        result.setStatusDistribution(statusDistribution);

        return result;
    }
}
