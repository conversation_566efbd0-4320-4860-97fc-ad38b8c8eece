package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.OutlierData;
import cn.facilityone.ai.dto.WorkOrderData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 离群数据检测工具类测试
 */
class OutlierDetectionUtilTest {

    private List<WorkOrderData> testWorkOrderList;

    @BeforeEach
    void setUp() {
        testWorkOrderList = createTestWorkOrderList();
    }

    /**
     * 测试处理时长离群检测
     */
    @Test
    void testDetectProcessingTimeOutliers() {
        List<OutlierData> outliers = OutlierDetectionUtil.detectProcessingTimeOutliers(testWorkOrderList);

        assertNotNull(outliers);
        assertTrue(outliers.size() > 0, "应该检测到处理时长离群数据");

        // 验证离群数据类型
        for (OutlierData outlier : outliers) {
            assertEquals(OutlierData.OutlierType.PROCESSING_TIME_OUTLIER, outlier.getOutlierType());
            assertNotNull(outlier.getWorkOrder());
            assertNotNull(outlier.getReason());
            assertNotNull(outlier.getOutlierScore());
            assertTrue(outlier.getOutlierScore() > 0);
        }
    }

    /**
     * 测试响应时间离群检测
     */
    @Test
    void testDetectResponseTimeOutliers() {
        List<OutlierData> outliers = OutlierDetectionUtil.detectResponseTimeOutliers(testWorkOrderList);

        assertNotNull(outliers);
        // 可能没有响应时间离群数据，这是正常的
        
        for (OutlierData outlier : outliers) {
            assertEquals(OutlierData.OutlierType.RESPONSE_TIME_OUTLIER, outlier.getOutlierType());
            assertNotNull(outlier.getWorkOrder());
            assertNotNull(outlier.getReason());
            assertNotNull(outlier.getOutlierScore());
        }
    }

    /**
     * 测试业务规则离群检测
     */
    @Test
    void testDetectBusinessRuleOutliers() {
        List<OutlierData> outliers = OutlierDetectionUtil.detectBusinessRuleOutliers(testWorkOrderList);

        assertNotNull(outliers);
        assertTrue(outliers.size() > 0, "应该检测到业务规则离群数据");

        for (OutlierData outlier : outliers) {
            assertEquals(OutlierData.OutlierType.BUSINESS_RULE_OUTLIER, outlier.getOutlierType());
            assertNotNull(outlier.getWorkOrder());
            assertNotNull(outlier.getReason());
            assertEquals(1.0, outlier.getOutlierScore()); // 业务规则异常统一为1.0
        }
    }

    /**
     * 测试空数据列表
     */
    @Test
    void testDetectOutliersWithEmptyList() {
        List<WorkOrderData> emptyList = new ArrayList<>();

        List<OutlierData> processingTimeOutliers = OutlierDetectionUtil.detectProcessingTimeOutliers(emptyList);
        List<OutlierData> responseTimeOutliers = OutlierDetectionUtil.detectResponseTimeOutliers(emptyList);
        List<OutlierData> businessRuleOutliers = OutlierDetectionUtil.detectBusinessRuleOutliers(emptyList);

        assertTrue(processingTimeOutliers.isEmpty());
        assertTrue(responseTimeOutliers.isEmpty());
        assertTrue(businessRuleOutliers.isEmpty());
    }

    /**
     * 测试数据不足的情况
     */
    @Test
    void testDetectOutliersWithInsufficientData() {
        List<WorkOrderData> smallList = new ArrayList<>();
        
        // 只添加2个工单，不足以进行统计分析
        WorkOrderData wo1 = new WorkOrderData();
        wo1.setOrderNumber("WO001");
        wo1.setWorkDurationMinutes(60);
        smallList.add(wo1);

        WorkOrderData wo2 = new WorkOrderData();
        wo2.setOrderNumber("WO002");
        wo2.setWorkDurationMinutes(90);
        smallList.add(wo2);

        List<OutlierData> outliers = OutlierDetectionUtil.detectProcessingTimeOutliers(smallList);
        assertTrue(outliers.isEmpty(), "数据不足时应该返回空列表");
    }

    /**
     * 创建测试用的工单数据列表
     */
    private List<WorkOrderData> createTestWorkOrderList() {
        List<WorkOrderData> workOrders = new ArrayList<>();

        // 正常处理时长的工单
        for (int i = 1; i <= 10; i++) {
            WorkOrderData wo = new WorkOrderData();
            wo.setOrderNumber("WO" + String.format("%03d", i));
            wo.setApplicant("申请人" + i);
            wo.setCreateDate(LocalDate.now().minusDays(i));
            wo.setCreateTime(LocalTime.of(9, 0));
            wo.setAcceptTime(LocalTime.of(9, 30)); // 30分钟响应时间
            wo.setWorkDurationMinutes(60 + i * 10); // 70-160分钟
            wo.setStatus("已完成");
            wo.setActualStartTime(LocalTime.of(10, 0));
            wo.setActualEndTime(LocalTime.of(11, 0).plusMinutes(i * 10));
            workOrders.add(wo);
        }

        // 添加处理时长异常的工单（过长）
        WorkOrderData longProcessingWo = new WorkOrderData();
        longProcessingWo.setOrderNumber("WO_LONG");
        longProcessingWo.setApplicant("申请人Long");
        longProcessingWo.setCreateDate(LocalDate.now());
        longProcessingWo.setCreateTime(LocalTime.of(9, 0));
        longProcessingWo.setAcceptTime(LocalTime.of(9, 30));
        longProcessingWo.setWorkDurationMinutes(600); // 10小时，明显异常
        longProcessingWo.setStatus("已完成");
        longProcessingWo.setActualStartTime(LocalTime.of(10, 0));
        longProcessingWo.setActualEndTime(LocalTime.of(20, 0));
        workOrders.add(longProcessingWo);

        // 添加处理时长异常的工单（过短）
        WorkOrderData shortProcessingWo = new WorkOrderData();
        shortProcessingWo.setOrderNumber("WO_SHORT");
        shortProcessingWo.setApplicant("申请人Short");
        shortProcessingWo.setCreateDate(LocalDate.now());
        shortProcessingWo.setCreateTime(LocalTime.of(9, 0));
        shortProcessingWo.setAcceptTime(LocalTime.of(9, 30));
        shortProcessingWo.setWorkDurationMinutes(5); // 5分钟，明显异常
        shortProcessingWo.setStatus("已完成");
        shortProcessingWo.setActualStartTime(LocalTime.of(10, 0));
        shortProcessingWo.setActualEndTime(LocalTime.of(10, 5));
        workOrders.add(shortProcessingWo);

        // 添加业务规则异常的工单（时间逻辑错误）
        WorkOrderData timeLogicErrorWo = new WorkOrderData();
        timeLogicErrorWo.setOrderNumber("WO_TIME_ERROR");
        timeLogicErrorWo.setApplicant("申请人TimeError");
        timeLogicErrorWo.setCreateDate(LocalDate.now());
        timeLogicErrorWo.setActualStartTime(LocalTime.of(15, 0)); // 开始时间晚于结束时间
        timeLogicErrorWo.setActualEndTime(LocalTime.of(14, 0));
        timeLogicErrorWo.setStatus("已完成");
        workOrders.add(timeLogicErrorWo);

        // 添加数据完整性异常的工单（缺少关键字段）
        WorkOrderData dataIntegrityErrorWo = new WorkOrderData();
        dataIntegrityErrorWo.setOrderNumber(""); // 工单号为空
        dataIntegrityErrorWo.setApplicant(""); // 申请人为空
        dataIntegrityErrorWo.setCreateDate(LocalDate.now());
        dataIntegrityErrorWo.setStatus("已完成");
        workOrders.add(dataIntegrityErrorWo);

        // 添加状态异常的工单（状态与时间不匹配）
        WorkOrderData statusErrorWo = new WorkOrderData();
        statusErrorWo.setOrderNumber("WO_STATUS_ERROR");
        statusErrorWo.setApplicant("申请人StatusError");
        statusErrorWo.setCreateDate(LocalDate.now());
        statusErrorWo.setStatus("已完成"); // 状态为已完成但没有结束时间
        // 故意不设置actualEndTime
        workOrders.add(statusErrorWo);

        return workOrders;
    }
}
