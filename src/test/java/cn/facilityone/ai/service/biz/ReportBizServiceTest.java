package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.dto.ReportAnalysisResult;
import cn.facilityone.ai.dto.WorkOrderAnalysisResult;
import cn.facilityone.ai.dto.WorkOrderData;
import cn.facilityone.ai.dto.OutlierData;
import cn.facilityone.ai.dto.OutlierStatistics;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.exception.AIServiceException;
import cn.facilityone.ai.exception.CSVParseException;
import cn.facilityone.ai.exception.ReportAnalysisException;
import cn.facilityone.ai.exception.UnsupportedFileFormatException;
import cn.facilityone.ai.service.db.FileService;
import cn.facilityone.ai.service.tool.ChartTool;
import cn.facilityone.ai.util.PromptsUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.web.client.ResourceAccessException;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ReportBizService 单元测试
 * 测试CSV解析功能、统计分析逻辑和AI服务调用
 */
@ExtendWith(MockitoExtension.class)
class ReportBizServiceTest {

    @Mock
    private FileService fileService;

    @Mock
    private FileBizService fileBizService;

    @Mock
    private ChatClient chatClient;

    @Mock
    private ChartTool chartTool;

    @Mock
    private ChatClient.ChatClientRequestSpec requestSpec;

    @Mock
    private ChatClient.CallResponseSpec callResponseSpec;

    private ReportBizService reportBizService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        reportBizService = new ReportBizService(fileService, fileBizService, chatClient, chartTool);
    }

    // ==================== 主要分析流程测试 ====================

    /**
     * 测试正常的报表分析流程
     */
    @Test
    void testAnalyzeReportByFileId_Success() throws Exception {
        // 准备测试数据
        Long fileId = 1L;
        FileEntity fileEntity = createTestFileEntity();
        File csvFile = createTestCSVFile();

        // Mock依赖服务
        when(fileService.getById(fileId)).thenReturn(fileEntity);
        when(fileBizService.getFile(fileEntity)).thenReturn(csvFile);
        
        // Mock AI服务
        mockAIService("这是AI生成的分析报告");

        // Mock PromptsUtil
        try (MockedStatic<PromptsUtil> mockedPromptsUtil = mockStatic(PromptsUtil.class)) {
            mockedPromptsUtil.when(() -> PromptsUtil.getPrompts(anyString()))
                    .thenReturn("分析以下工单数据：{{工单数据}}");

            // 执行测试
            ReportAnalysisResult result = reportBizService.analyzeReportByFileId(fileId);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getStatisticalData());
            assertNotNull(result.getAiAnalysis());
            assertEquals("test.csv", result.getFileName());
            assertEquals("这是AI生成的分析报告", result.getAiAnalysis());
            assertTrue(result.getStatisticalData().getTotalCount() > 0);
        }
    }

    /**
     * 测试文件不存在的情况
     */
    @Test
    void testAnalyzeReportByFileId_FileNotFound() {
        // 准备测试数据
        Long fileId = 999L;

        // Mock文件不存在
        when(fileService.getById(fileId)).thenReturn(null);

        // 执行测试并验证异常
        FileNotFoundException exception = assertThrows(FileNotFoundException.class, () -> {
            reportBizService.analyzeReportByFileId(fileId);
        });

        assertTrue(exception.getMessage().contains("文件不存在或已被删除"));
    }

    /**
     * 测试不支持的文件格式
     */
    @Test
    void testAnalyzeReportByFileId_UnsupportedFileFormat() {
        // 准备测试数据
        Long fileId = 1L;
        FileEntity fileEntity = createTestFileEntity();
        fileEntity.setName("test.txt"); // 不支持的格式

        // Mock依赖服务
        when(fileService.getById(fileId)).thenReturn(fileEntity);

        // 执行测试并验证异常
        UnsupportedFileFormatException exception = assertThrows(UnsupportedFileFormatException.class, () -> {
            reportBizService.analyzeReportByFileId(fileId);
        });

        assertTrue(exception.getMessage().contains("不支持的文件格式"));
    }

    /**
     * 测试AI服务异常
     */
    @Test
    void testAnalyzeReportByFileId_AIServiceException() throws Exception {
        // 准备测试数据
        Long fileId = 1L;
        FileEntity fileEntity = createTestFileEntity();
        File csvFile = createTestCSVFile();

        // Mock依赖服务
        when(fileService.getById(fileId)).thenReturn(fileEntity);
        when(fileBizService.getFile(fileEntity)).thenReturn(csvFile);

        // Mock AI服务异常
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenThrow(new RuntimeException("AI服务连接失败"));

        // Mock PromptsUtil
        try (MockedStatic<PromptsUtil> mockedPromptsUtil = mockStatic(PromptsUtil.class)) {
            mockedPromptsUtil.when(() -> PromptsUtil.getPrompts(anyString()))
                    .thenReturn("分析以下工单数据：{{工单数据}}");

            // 执行测试并验证异常
            ReportAnalysisException exception = assertThrows(ReportAnalysisException.class, () -> {
                reportBizService.analyzeReportByFileId(fileId);
            });

            assertTrue(exception.getMessage().contains("AI分析服务异常"));
        }
    }

    // ==================== CSV解析功能测试 ====================

    /**
     * 测试正常CSV文件解析
     */
    @Test
    void testParseCSVFile_Success() throws Exception {
        // 创建测试CSV文件
        File csvFile = createTestCSVFile();

        // 使用反射调用私有方法
        Method parseCSVFileMethod = ReportBizService.class.getDeclaredMethod("parseCSVFile", File.class);
        parseCSVFileMethod.setAccessible(true);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<WorkOrderData> result = (List<WorkOrderData>) parseCSVFileMethod.invoke(reportBizService, csvFile);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);
        
        WorkOrderData firstOrder = result.get(0);
        assertNotNull(firstOrder.getOrderNumber());
        assertNotNull(firstOrder.getApplicant());
    }

    /**
     * 测试空CSV文件解析
     */
    @Test
    void testParseCSVFile_EmptyFile() throws Exception {
        // 创建空CSV文件
        File emptyFile = createEmptyCSVFile();

        // 使用反射调用私有方法
        Method parseCSVFileMethod = ReportBizService.class.getDeclaredMethod("parseCSVFile", File.class);
        parseCSVFileMethod.setAccessible(true);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<WorkOrderData> result = (List<WorkOrderData>) parseCSVFileMethod.invoke(reportBizService, emptyFile);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }



    // ==================== 统计分析功能测试 ====================

    /**
     * 测试统计分析功能
     */
    @Test
    void testPerformStatisticalAnalysis_Success() throws Exception {
        // 准备测试数据
        List<WorkOrderData> workOrderList = createTestWorkOrderList();

        // 使用反射调用私有方法
        Method performStatisticalAnalysisMethod = ReportBizService.class
                .getDeclaredMethod("performStatisticalAnalysis", List.class);
        performStatisticalAnalysisMethod.setAccessible(true);

        // 执行测试
        WorkOrderAnalysisResult result = (WorkOrderAnalysisResult) performStatisticalAnalysisMethod
                .invoke(reportBizService, workOrderList);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalCount());
        
        // 验证状态分布
        assertNotNull(result.getStatusDistribution());
        assertEquals(2, result.getStatusDistribution().get("已完成").intValue());
        assertEquals(1, result.getStatusDistribution().get("进行中").intValue());
        
        // 验证优先级分布
        assertNotNull(result.getPriorityDistribution());
        assertEquals(1, result.getPriorityDistribution().get("高").intValue());
        assertEquals(2, result.getPriorityDistribution().get("中").intValue());
    }

    /**
     * 测试空数据的统计分析
     */
    @Test
    void testPerformStatisticalAnalysis_EmptyData() throws Exception {
        // 准备空数据
        List<WorkOrderData> emptyList = new ArrayList<>();

        // 使用反射调用私有方法
        Method performStatisticalAnalysisMethod = ReportBizService.class
                .getDeclaredMethod("performStatisticalAnalysis", List.class);
        performStatisticalAnalysisMethod.setAccessible(true);

        // 执行测试
        WorkOrderAnalysisResult result = (WorkOrderAnalysisResult) performStatisticalAnalysisMethod
                .invoke(reportBizService, emptyList);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalCount());
    }

    /**
     * 测试处理时长分析
     */
    @Test
    void testAnalyzeProcessingTime() throws Exception {
        // 准备包含处理时长的测试数据
        List<WorkOrderData> workOrderList = createWorkOrderListWithProcessingTime();

        // 使用反射调用私有方法
        Method performStatisticalAnalysisMethod = ReportBizService.class
                .getDeclaredMethod("performStatisticalAnalysis", List.class);
        performStatisticalAnalysisMethod.setAccessible(true);

        // 执行测试
        WorkOrderAnalysisResult result = (WorkOrderAnalysisResult) performStatisticalAnalysisMethod
                .invoke(reportBizService, workOrderList);

        // 验证处理时长分析结果
        assertNotNull(result);
        assertTrue(result.getAverageProcessingTime() > 0);
        assertTrue(result.getMaxProcessingTime() > 0);
        assertTrue(result.getMinProcessingTime() > 0);
    }

    // ==================== AI服务调用测试 ====================

    /**
     * 测试AI分析报告生成
     */
    @Test
    void testGenerateAIAnalysis_Success() throws Exception {
        // 准备测试数据
        WorkOrderAnalysisResult statisticalResult = createTestStatisticalResult();
        
        // Mock AI服务
        mockAIService("这是AI生成的详细分析报告");

        // Mock PromptsUtil
        try (MockedStatic<PromptsUtil> mockedPromptsUtil = mockStatic(PromptsUtil.class)) {
            mockedPromptsUtil.when(() -> PromptsUtil.getPrompts("fm/report-analysis1"))
                    .thenReturn("分析以下工单数据：{{工单数据}}");

            // 使用反射调用私有方法
            Method generateAIAnalysisMethod = ReportBizService.class
                    .getDeclaredMethod("generateAIAnalysis", WorkOrderAnalysisResult.class, String.class);
            generateAIAnalysisMethod.setAccessible(true);

            // 执行测试
            String result = (String) generateAIAnalysisMethod
                    .invoke(reportBizService, statisticalResult, "fm/report-analysis1");

            // 验证结果
            assertNotNull(result);
            assertEquals("这是AI生成的详细分析报告", result);
        }
    }

    /**
     * 测试AI服务连接异常
     */
    @Test
    void testGenerateAIAnalysis_ConnectionException() throws Exception {
        // 准备测试数据
        WorkOrderAnalysisResult statisticalResult = createTestStatisticalResult();

        // Mock AI服务连接异常
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenThrow(new ResourceAccessException("连接超时"));

        // Mock PromptsUtil
        try (MockedStatic<PromptsUtil> mockedPromptsUtil = mockStatic(PromptsUtil.class)) {
            mockedPromptsUtil.when(() -> PromptsUtil.getPrompts("fm/report-analysis1"))
                    .thenReturn("分析以下工单数据：{{工单数据}}");

            // 使用反射调用私有方法
            Method generateAIAnalysisMethod = ReportBizService.class
                    .getDeclaredMethod("generateAIAnalysis", WorkOrderAnalysisResult.class, String.class);
            generateAIAnalysisMethod.setAccessible(true);

            // 执行测试并验证异常
            Exception exception = assertThrows(Exception.class, () -> {
                generateAIAnalysisMethod.invoke(reportBizService, statisticalResult, "fm/report-analysis1");
            });

            // 验证是AIServiceException的包装异常
            assertTrue(exception.getCause() instanceof AIServiceException);
        }
    }

    /**
     * 测试AI服务返回空结果
     */
    @Test
    void testGenerateAIAnalysis_EmptyResponse() throws Exception {
        // 准备测试数据
        WorkOrderAnalysisResult statisticalResult = createTestStatisticalResult();

        // Mock AI服务返回空结果
        mockAIService("");

        // Mock PromptsUtil
        try (MockedStatic<PromptsUtil> mockedPromptsUtil = mockStatic(PromptsUtil.class)) {
            mockedPromptsUtil.when(() -> PromptsUtil.getPrompts("fm/report-analysis1"))
                    .thenReturn("分析以下工单数据：{{工单数据}}");

            // 使用反射调用私有方法
            Method generateAIAnalysisMethod = ReportBizService.class
                    .getDeclaredMethod("generateAIAnalysis", WorkOrderAnalysisResult.class, String.class);
            generateAIAnalysisMethod.setAccessible(true);

            // 执行测试并验证异常
            Exception exception = assertThrows(Exception.class, () -> {
                generateAIAnalysisMethod.invoke(reportBizService, statisticalResult, "fm/report-analysis1");
            });

            // 验证是AIServiceException的包装异常
            assertTrue(exception.getCause() instanceof AIServiceException);
        }
    }

    // ==================== 数据解析测试 ====================

    /**
     * 测试日期解析
     */
    @Test
    void testParseDate() throws Exception {
        // 使用反射调用私有方法
        Method parseDateMethod = ReportBizService.class.getDeclaredMethod("parseDate", String.class);
        parseDateMethod.setAccessible(true);

        // 测试各种日期格式
        LocalDate result1 = (LocalDate) parseDateMethod.invoke(reportBizService, "15/03/23");
        assertNotNull(result1);
        assertEquals(15, result1.getDayOfMonth());
        assertEquals(3, result1.getMonthValue());

        LocalDate result2 = (LocalDate) parseDateMethod.invoke(reportBizService, "2023-03-15");
        assertNotNull(result2);
        assertEquals(15, result2.getDayOfMonth());
        assertEquals(3, result2.getMonthValue());

        // 测试无效日期
        LocalDate result3 = (LocalDate) parseDateMethod.invoke(reportBizService, "invalid-date");
        assertNull(result3);

        // 测试空字符串
        LocalDate result4 = (LocalDate) parseDateMethod.invoke(reportBizService, "");
        assertNull(result4);
    }

    /**
     * 测试时间解析
     */
    @Test
    void testParseTime() throws Exception {
        // 使用反射调用私有方法
        Method parseTimeMethod = ReportBizService.class.getDeclaredMethod("parseTime", String.class);
        parseTimeMethod.setAccessible(true);

        // 测试各种时间格式
        LocalTime result1 = (LocalTime) parseTimeMethod.invoke(reportBizService, "14:30:45");
        assertNotNull(result1);
        assertEquals(14, result1.getHour());
        assertEquals(30, result1.getMinute());
        assertEquals(45, result1.getSecond());

        LocalTime result2 = (LocalTime) parseTimeMethod.invoke(reportBizService, "09:15");
        assertNotNull(result2);
        assertEquals(9, result2.getHour());
        assertEquals(15, result2.getMinute());

        // 测试无效时间
        LocalTime result3 = (LocalTime) parseTimeMethod.invoke(reportBizService, "invalid-time");
        assertNull(result3);

        // 测试空字符串
        LocalTime result4 = (LocalTime) parseTimeMethod.invoke(reportBizService, "");
        assertNull(result4);
    }

    /**
     * 测试整数解析
     */
    @Test
    void testParseInteger() throws Exception {
        // 使用反射调用私有方法
        Method parseIntegerMethod = ReportBizService.class.getDeclaredMethod("parseInteger", String.class);
        parseIntegerMethod.setAccessible(true);

        // 测试正常整数
        Integer result1 = (Integer) parseIntegerMethod.invoke(reportBizService, "123");
        assertNotNull(result1);
        assertEquals(123, result1.intValue());

        // 测试负数
        Integer result2 = (Integer) parseIntegerMethod.invoke(reportBizService, "-456");
        assertNotNull(result2);
        assertEquals(-456, result2.intValue());

        // 测试无效整数
        Integer result3 = (Integer) parseIntegerMethod.invoke(reportBizService, "abc");
        assertNull(result3);

        // 测试空字符串
        Integer result4 = (Integer) parseIntegerMethod.invoke(reportBizService, "");
        assertNull(result4);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的文件实体
     */
    private FileEntity createTestFileEntity() {
        FileEntity fileEntity = new FileEntity();
        fileEntity.setId(1L);
        fileEntity.setName("test.csv");
        fileEntity.setPath("/test/path/test.csv");
        fileEntity.setType(FileEntity.FileType.DOCUMENT);
        return fileEntity;
    }

    /**
     * 创建测试用的CSV文件
     */
    private File createTestCSVFile() throws IOException {
        Path csvPath = tempDir.resolve("test.csv");
        String csvContent = """
                工单号,申请人,创建日期,创建时间,服务类型,优先级,工单状态,工作时长
                WO001,张三,15/03/23,09:30:00,维修,高,已完成,120
                WO002,李四,16/03/23,10:15:00,清洁,中,已完成,90
                WO003,王五,17/03/23,14:20:00,安装,中,进行中,
                """;
        Files.write(csvPath, csvContent.getBytes());
        return csvPath.toFile();
    }

    /**
     * 创建空的CSV文件
     */
    private File createEmptyCSVFile() throws IOException {
        Path csvPath = tempDir.resolve("empty.csv");
        Files.write(csvPath, "".getBytes());
        return csvPath.toFile();
    }

    /**
     * 创建格式错误的CSV文件
     */
    private File createInvalidCSVFile() throws IOException {
        Path csvPath = tempDir.resolve("invalid.csv");
        // 创建一个只有表头但所有数据行都是空的CSV文件，这会导致解析失败
        String invalidContent = """
                工单号,申请人,创建日期
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                ,,
                """;
        Files.write(csvPath, invalidContent.getBytes());
        return csvPath.toFile();
    }

    /**
     * 创建测试用的工单数据列表
     */
    private List<WorkOrderData> createTestWorkOrderList() {
        List<WorkOrderData> list = new ArrayList<>();

        WorkOrderData order1 = new WorkOrderData();
        order1.setOrderNumber("WO001");
        order1.setApplicant("张三");
        order1.setStatus("已完成");
        order1.setPriority("高");
        order1.setCreateDate(LocalDate.of(2023, 3, 15));
        list.add(order1);

        WorkOrderData order2 = new WorkOrderData();
        order2.setOrderNumber("WO002");
        order2.setApplicant("李四");
        order2.setStatus("已完成");
        order2.setPriority("中");
        order2.setCreateDate(LocalDate.of(2023, 3, 16));
        list.add(order2);

        WorkOrderData order3 = new WorkOrderData();
        order3.setOrderNumber("WO003");
        order3.setApplicant("王五");
        order3.setStatus("进行中");
        order3.setPriority("中");
        order3.setCreateDate(LocalDate.of(2023, 3, 17));
        list.add(order3);

        return list;
    }

    /**
     * 创建包含处理时长的工单数据列表
     */
    private List<WorkOrderData> createWorkOrderListWithProcessingTime() {
        List<WorkOrderData> list = createTestWorkOrderList();
        list.get(0).setWorkDurationMinutes(120);
        list.get(1).setWorkDurationMinutes(90);
        list.get(2).setWorkDurationMinutes(150);
        return list;
    }

    /**
     * 创建测试用的统计分析结果
     */
    private WorkOrderAnalysisResult createTestStatisticalResult() {
        WorkOrderAnalysisResult result = new WorkOrderAnalysisResult();
        result.setTotalCount(100);

        Map<String, Integer> statusDistribution = new HashMap<>();
        statusDistribution.put("已完成", 60);
        statusDistribution.put("进行中", 30);
        statusDistribution.put("待处理", 10);
        result.setStatusDistribution(statusDistribution);

        Map<String, Integer> priorityDistribution = new HashMap<>();
        priorityDistribution.put("高", 20);
        priorityDistribution.put("中", 50);
        priorityDistribution.put("低", 30);
        result.setPriorityDistribution(priorityDistribution);

        result.setAverageProcessingTime(120.5);
        result.setCompletionRate(85.0);

        return result;
    }

    /**
     * Mock AI服务调用
     */
    private void mockAIService(String response) {
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenReturn(response);
    }

    // ==================== 离群数据分析测试 ====================

    /**
     * 测试离群数据分析功能
     */
    @Test
    void testAnalyzeOutliers_Success() throws Exception {
        // 准备包含离群数据的测试数据
        List<WorkOrderData> workOrderList = createWorkOrderListWithOutliers();

        // 使用反射调用私有方法
        Method performStatisticalAnalysisMethod = ReportBizService.class
                .getDeclaredMethod("performStatisticalAnalysis", List.class);
        performStatisticalAnalysisMethod.setAccessible(true);

        // 执行测试
        WorkOrderAnalysisResult result = (WorkOrderAnalysisResult) performStatisticalAnalysisMethod
                .invoke(reportBizService, workOrderList);

        // 验证离群数据分析结果
        assertNotNull(result);
        assertNotNull(result.getOutlierWorkOrders(), "离群工单列表不应为空");
        assertNotNull(result.getOutlierStatistics(), "离群数据统计不应为空");
        assertNotNull(result.getTotalOutlierCount(), "总离群数据数量不应为空");
        assertNotNull(result.getOutlierPercentage(), "离群数据占比不应为空");

        assertTrue(result.getTotalOutlierCount() > 0, "应该检测到离群数据");
        assertTrue(result.getOutlierPercentage() >= 0 && result.getOutlierPercentage() <= 100,
                "离群数据占比应该在0-100之间");

        // 验证离群数据统计信息
        OutlierStatistics stats = result.getOutlierStatistics();
        assertNotNull(stats.getOutlierTypeDistribution(), "离群类型分布不应为空");
        assertNotNull(stats.getSeverityDistribution(), "严重程度分布不应为空");
        assertEquals(result.getTotalOutlierCount(), stats.getTotalOutlierCount(),
                "统计信息中的总数应与结果中的总数一致");
    }

    /**
     * 测试空数据的离群分析
     */
    @Test
    void testAnalyzeOutliers_EmptyData() throws Exception {
        List<WorkOrderData> emptyList = new ArrayList<>();

        // 使用反射调用私有方法
        Method performStatisticalAnalysisMethod = ReportBizService.class
                .getDeclaredMethod("performStatisticalAnalysis", List.class);
        performStatisticalAnalysisMethod.setAccessible(true);

        // 执行测试
        WorkOrderAnalysisResult result = (WorkOrderAnalysisResult) performStatisticalAnalysisMethod
                .invoke(reportBizService, emptyList);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalCount());
        // 空数据时离群分析应该被跳过，不会设置离群数据字段
    }

    /**
     * 测试离群数据统计计算
     */
    @Test
    void testCalculateOutlierStatistics() throws Exception {
        // 准备测试数据
        List<OutlierData> allOutliers = createTestOutlierData();
        List<OutlierData> processingTimeOutliers = allOutliers.subList(0, 2);
        List<OutlierData> responseTimeOutliers = allOutliers.subList(2, 3);
        List<OutlierData> businessRuleOutliers = allOutliers.subList(3, 5);

        // 使用反射调用私有方法
        Method calculateOutlierStatisticsMethod = ReportBizService.class
                .getDeclaredMethod("calculateOutlierStatistics", List.class, List.class, List.class, List.class, int.class);
        calculateOutlierStatisticsMethod.setAccessible(true);

        // 执行测试
        OutlierStatistics stats = (OutlierStatistics) calculateOutlierStatisticsMethod
                .invoke(reportBizService, allOutliers, processingTimeOutliers, responseTimeOutliers, businessRuleOutliers, 100);

        // 验证结果
        assertNotNull(stats);
        assertEquals(allOutliers.size(), stats.getTotalOutlierCount());
        assertEquals(5.0, stats.getOutlierPercentage()); // 5个离群数据 / 100个总数据 = 5%
        assertNotNull(stats.getOutlierTypeDistribution());
        assertNotNull(stats.getSeverityDistribution());
    }

    /**
     * 创建包含离群数据的工单列表
     */
    private List<WorkOrderData> createWorkOrderListWithOutliers() {
        List<WorkOrderData> workOrders = new ArrayList<>();

        // 正常工单
        for (int i = 1; i <= 10; i++) {
            WorkOrderData wo = new WorkOrderData();
            wo.setOrderNumber("WO" + String.format("%03d", i));
            wo.setApplicant("申请人" + i);
            wo.setCreateDate(LocalDate.now().minusDays(i));
            wo.setCreateTime(LocalTime.of(9, 0));
            wo.setAcceptTime(LocalTime.of(9, 30));
            wo.setWorkDurationMinutes(60 + i * 5); // 65-115分钟
            wo.setStatus("已完成");
            wo.setActualStartTime(LocalTime.of(10, 0));
            wo.setActualEndTime(LocalTime.of(11, 0).plusMinutes(i * 5));
            workOrders.add(wo);
        }

        // 添加处理时长异常工单
        WorkOrderData longProcessingWo = new WorkOrderData();
        longProcessingWo.setOrderNumber("WO_LONG");
        longProcessingWo.setApplicant("申请人Long");
        longProcessingWo.setWorkDurationMinutes(500); // 异常长的处理时间
        longProcessingWo.setStatus("已完成");
        workOrders.add(longProcessingWo);

        // 添加业务规则异常工单
        WorkOrderData timeErrorWo = new WorkOrderData();
        timeErrorWo.setOrderNumber("WO_TIME_ERROR");
        timeErrorWo.setApplicant("申请人TimeError");
        timeErrorWo.setActualStartTime(LocalTime.of(15, 0)); // 开始时间晚于结束时间
        timeErrorWo.setActualEndTime(LocalTime.of(14, 0));
        timeErrorWo.setStatus("已完成");
        workOrders.add(timeErrorWo);

        // 添加数据完整性异常工单
        WorkOrderData dataErrorWo = new WorkOrderData();
        dataErrorWo.setOrderNumber(""); // 工单号为空
        dataErrorWo.setApplicant(""); // 申请人为空
        dataErrorWo.setStatus("已完成");
        workOrders.add(dataErrorWo);

        return workOrders;
    }

    /**
     * 创建测试用的离群数据
     */
    private List<OutlierData> createTestOutlierData() {
        List<OutlierData> outliers = new ArrayList<>();

        // 处理时长离群数据
        OutlierData processingOutlier1 = new OutlierData();
        processingOutlier1.setOutlierType(OutlierData.OutlierType.PROCESSING_TIME_OUTLIER);
        processingOutlier1.setOutlierScore(3.5);
        processingOutlier1.setReason("处理时长过长");
        outliers.add(processingOutlier1);

        OutlierData processingOutlier2 = new OutlierData();
        processingOutlier2.setOutlierType(OutlierData.OutlierType.PROCESSING_TIME_OUTLIER);
        processingOutlier2.setOutlierScore(2.1);
        processingOutlier2.setReason("处理时长过短");
        outliers.add(processingOutlier2);

        // 响应时间离群数据
        OutlierData responseOutlier = new OutlierData();
        responseOutlier.setOutlierType(OutlierData.OutlierType.RESPONSE_TIME_OUTLIER);
        responseOutlier.setOutlierScore(2.8);
        responseOutlier.setReason("响应时间异常");
        outliers.add(responseOutlier);

        // 业务规则离群数据
        OutlierData businessOutlier1 = new OutlierData();
        businessOutlier1.setOutlierType(OutlierData.OutlierType.BUSINESS_RULE_OUTLIER);
        businessOutlier1.setOutlierScore(1.0);
        businessOutlier1.setReason("时间逻辑错误");
        outliers.add(businessOutlier1);

        OutlierData businessOutlier2 = new OutlierData();
        businessOutlier2.setOutlierType(OutlierData.OutlierType.BUSINESS_RULE_OUTLIER);
        businessOutlier2.setOutlierScore(1.0);
        businessOutlier2.setReason("数据完整性错误");
        outliers.add(businessOutlier2);

        return outliers;
    }
}