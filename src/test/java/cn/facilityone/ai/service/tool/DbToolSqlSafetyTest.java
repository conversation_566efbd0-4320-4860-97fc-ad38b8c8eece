package cn.facilityone.ai.service.tool;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;
import java.util.Arrays;
import java.util.List;

/**
 * DbTool SQL安全检查专项测试
 * 主要测试SQL安全检查的准确性，避免误报
 */
class DbToolSqlSafetyTest {

    private DbTool dbTool;

    @BeforeEach
    void setUp() {
        dbTool = new DbTool();
    }

    @Test
    void testOriginalProblemSql() {
        // 测试原始问题中的SQL：包含deleted字段不应该被误报
        String sql = "select id,user_name,real_name,phone,proj_ids from sys_user where user_name like ? AND deleted = false";
        List<Object> parameters = Arrays.asList("%test%");
        
        DbTool.ParameterizedRequest request = new DbTool.ParameterizedRequest(
            "***********************************",
            "root",
            "password",
            sql,
            parameters,
            100
        );

        // 这个请求应该能正常创建，不会因为包含"deleted"字段而被误报
        assertNotNull(request);
        assertEquals(sql, request.sql());
        assertEquals(1, request.parameters().size());
        
        // 验证SQL确实包含deleted但不是DELETE关键词
        assertTrue(sql.toLowerCase().contains("deleted"));
        assertFalse(sql.toLowerCase().matches(".*\\bdelete\\b.*"));
    }

    @Test
    void testSafeFieldNames() {
        // 测试包含类似危险关键词但实际是字段名的安全SQL
        String[] safeSqls = {
            "SELECT * FROM users WHERE deleted = ?",
            "SELECT * FROM orders WHERE updated_at > ?",
            "SELECT * FROM products WHERE created_by = ?",
            "SELECT * FROM logs WHERE truncated_message IS NOT NULL",
            "SELECT * FROM audit WHERE inserted_date BETWEEN ? AND ?",
            "SELECT * FROM config WHERE altered_by = ?",
            "SELECT * FROM backup WHERE dropped_tables IS NULL"
        };

        for (String sql : safeSqls) {
            List<Object> parameters = Arrays.asList("test_value");
            
            DbTool.Request request = new DbTool.Request(
                "***********************************",
                "root",
                "password",
                sql,
                100,
                parameters
            );

            assertNotNull(request, "安全的SQL不应该被拒绝: " + sql);
            assertEquals(sql, request.sql());
        }
    }

    @Test
    void testActualDangerousSql() {
        // 测试真正危险的SQL应该被检测到（通过模拟执行来测试）
        String[] dangerousSqls = {
            "DROP TABLE users",
            "DELETE FROM users WHERE id = 1", 
            "INSERT INTO users VALUES (1, 'test')",
            "UPDATE users SET name = 'test' WHERE id = 1",
            "ALTER TABLE users ADD COLUMN test VARCHAR(50)",
            "CREATE TABLE test (id INT PRIMARY KEY)",
            "TRUNCATE TABLE users"
        };

        for (String dangerousSql : dangerousSqls) {
            // 验证这些SQL确实包含独立的危险关键词
            String upperSql = dangerousSql.toUpperCase();
            boolean containsDangerousKeyword = 
                upperSql.matches(".*\\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE)\\b.*");
            assertTrue(containsDangerousKeyword, 
                "应该检测到危险关键词: " + dangerousSql);
        }
    }

    @Test
    void testComplexSafeQueries() {
        // 测试复杂但安全的查询
        String[] complexSafeSqls = {
            "SELECT u.id, u.user_name, u.deleted, p.name FROM sys_user u JOIN projects p ON u.proj_ids LIKE CONCAT('%', p.id, '%') WHERE u.deleted = ? AND p.created_at > ?",
            "SELECT COUNT(*) FROM orders o WHERE o.updated_by IS NOT NULL AND o.inserted_date BETWEEN ? AND ? AND o.truncated_reason IS NULL",
            "SELECT * FROM (SELECT id, name, deleted FROM users WHERE active = ?) AS active_users WHERE deleted = false",
            "WITH deleted_users AS (SELECT * FROM users WHERE deleted = true) SELECT COUNT(*) FROM deleted_users WHERE created_at > ?"
        };

        for (String sql : complexSafeSqls) {
            List<Object> parameters = Arrays.asList(false, "2023-01-01");
            
            DbTool.Request request = new DbTool.Request(
                "***********************************",
                "root", 
                "password",
                sql,
                100,
                parameters
            );

            assertNotNull(request, "复杂但安全的SQL不应该被拒绝: " + sql);
        }
    }

    @Test
    void testWordBoundaryMatching() {
        // 测试单词边界匹配的准确性
        
        // 这些应该是安全的（包含类似单词但不是独立关键词）
        String[] safeWords = {
            "deleted", "updated", "created", "inserted", "truncated", "altered"
        };
        
        for (String word : safeWords) {
            String sql = String.format("SELECT * FROM table WHERE %s = ?", word);
            
            // 验证不会被误报为危险关键词
            boolean isIndependentKeyword = sql.toUpperCase()
                .matches(".*\\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE)\\b.*");
            assertFalse(isIndependentKeyword, 
                "字段名不应该被误报为危险关键词: " + word);
        }
        
        // 这些应该被检测为危险的（独立的关键词）
        String[] dangerousWords = {
            "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE", "TRUNCATE", "DROP"
        };
        
        for (String word : dangerousWords) {
            String sql = String.format("%s FROM table WHERE id = 1", word);
            
            // 验证会被检测为危险关键词
            boolean isIndependentKeyword = sql.toUpperCase()
                .matches(".*\\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE)\\b.*");
            assertTrue(isIndependentKeyword, 
                "独立的危险关键词应该被检测到: " + word);
        }
    }

    @Test
    void testCaseInsensitiveMatching() {
        // 测试大小写不敏感的匹配
        String[] variations = {
            "select * from users where deleted = false",
            "SELECT * FROM users WHERE deleted = false", 
            "Select * From users Where deleted = false",
            "sElEcT * fRoM users wHeRe deleted = false"
        };

        for (String sql : variations) {
            List<Object> parameters = Arrays.asList();
            
            DbTool.Request request = new DbTool.Request(
                "***********************************",
                "root",
                "password", 
                sql,
                100,
                parameters
            );

            assertNotNull(request, "不同大小写的安全SQL都应该被接受: " + sql);
            
            // 验证都不包含独立的危险关键词
            boolean containsDangerousKeyword = sql.toUpperCase()
                .matches(".*\\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE)\\b.*");
            assertFalse(containsDangerousKeyword, 
                "不应该误报包含deleted字段的SQL: " + sql);
        }
    }
}
