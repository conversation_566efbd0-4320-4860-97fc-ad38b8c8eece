package cn.facilityone.ai.service.tool;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;
import java.util.Arrays;
import java.util.List;

/**
 * DbTool 测试类
 * 主要测试参数化查询功能和SQL注入防护
 */
class DbToolTest {

    private DbTool dbTool;

    @BeforeEach
    void setUp() {
        dbTool = new DbTool();
    }

    @Test
    void testParameterizedRequestCreation() {
        // 测试参数化请求对象创建
        List<Object> parameters = Arrays.asList("active", 18, "admin");
        
        DbTool.ParameterizedRequest request = new DbTool.ParameterizedRequest(
            "***********************************",
            "root",
            "password",
            "SELECT * FROM users WHERE status = ? AND age > ? AND role = ?",
            parameters,
            100
        );

        assertNotNull(request);
        assertEquals("***********************************", request.url());
        assertEquals("root", request.username());
        assertEquals("password", request.password());
        assertEquals("SELECT * FROM users WHERE status = ? AND age > ? AND role = ?", request.sql());
        assertEquals(3, request.parameters().size());
        assertEquals("active", request.parameters().get(0));
        assertEquals(18, request.parameters().get(1));
        assertEquals("admin", request.parameters().get(2));
        assertEquals(100, request.limit());
    }

    @Test
    void testRequestWithParameters() {
        // 测试普通Request对象包含参数
        List<Object> parameters = Arrays.asList("test", 123);
        
        DbTool.Request request = new DbTool.Request(
            "***********************************",
            "root",
            "password",
            "SELECT * FROM table WHERE name = ? AND id = ?",
            50,
            parameters
        );

        assertNotNull(request);
        assertEquals(2, request.parameters().size());
        assertEquals("test", request.parameters().get(0));
        assertEquals(123, request.parameters().get(1));
    }

    @Test
    void testSqlValidation() {
        // 测试SQL语句验证
        // 这里可以添加更多的单元测试来验证SQL安全检查逻辑
        
        // 测试正常的SELECT语句
        String validSql = "SELECT * FROM users WHERE id = ?";
        assertTrue(validSql.toUpperCase().trim().startsWith("SELECT"));
        
        // 测试危险的SQL语句
        String dangerousSql = "DROP TABLE users";
        assertTrue(dangerousSql.toUpperCase().contains("DROP"));
    }

    @Test
    void testParameterPlaceholderCount() {
        // 测试参数占位符数量计算
        String sql1 = "SELECT * FROM users WHERE id = ?";
        long count1 = sql1.chars().filter(ch -> ch == '?').count();
        assertEquals(1, count1);
        
        String sql2 = "SELECT * FROM users WHERE name = ? AND age > ? AND status = ?";
        long count2 = sql2.chars().filter(ch -> ch == '?').count();
        assertEquals(3, count2);
        
        String sql3 = "SELECT * FROM users";
        long count3 = sql3.chars().filter(ch -> ch == '?').count();
        assertEquals(0, count3);
    }

    @Test
    void testParameterTypes() {
        // 测试不同类型的参数
        List<Object> mixedParameters = Arrays.asList(
            "string_value",      // String
            42,                  // Integer
            3.14,               // Double
            true,               // Boolean
            null                // Null
        );

        assertEquals(5, mixedParameters.size());
        assertTrue(mixedParameters.get(0) instanceof String);
        assertTrue(mixedParameters.get(1) instanceof Integer);
        assertTrue(mixedParameters.get(2) instanceof Double);
        assertTrue(mixedParameters.get(3) instanceof Boolean);
        assertNull(mixedParameters.get(4));
    }

    @Test
    void testSqlSafetyWithDeletedField() {
        // 测试包含 deleted 字段的SQL不会被误报为包含 DELETE 关键词
        String sqlWithDeletedField = "SELECT id,user_name,real_name,phone,proj_ids FROM sys_user WHERE user_name LIKE ? AND deleted = false";

        // 这个SQL应该是安全的，不应该被误报
        // 注意：这里我们无法直接调用私有方法，但可以通过公共方法间接测试

        // 创建一个包含 deleted 字段的参数化请求
        List<Object> parameters = Arrays.asList("%test%");
        DbTool.ParameterizedRequest request = new DbTool.ParameterizedRequest(
            "***********************************",
            "root",
            "password",
            sqlWithDeletedField,
            parameters,
            100
        );

        assertNotNull(request);
        assertEquals(sqlWithDeletedField, request.sql());
        assertEquals(1, request.parameters().size());

        // 验证SQL中确实包含 "deleted" 但不包含独立的 "DELETE"
        assertTrue(request.sql().toLowerCase().contains("deleted"));
        assertFalse(request.sql().toLowerCase().matches(".*\\bdelete\\b.*"));
    }

    @Test
    void testDangerousSqlDetection() {
        // 测试真正危险的SQL应该被检测到
        String[] dangerousSqls = {
            "DROP TABLE users",
            "DELETE FROM users WHERE id = 1",
            "INSERT INTO users VALUES (1, 'test')",
            "UPDATE users SET name = 'test'",
            "ALTER TABLE users ADD COLUMN test VARCHAR(50)",
            "CREATE TABLE test (id INT)",
            "TRUNCATE TABLE users"
        };

        for (String dangerousSql : dangerousSqls) {
            // 验证这些SQL包含危险关键词
            String upperSql = dangerousSql.toUpperCase();
            boolean containsDangerousKeyword =
                upperSql.matches(".*\\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE)\\b.*");
            assertTrue(containsDangerousKeyword, "应该检测到危险关键词: " + dangerousSql);
        }
    }

    @Test
    void testSafeSqlWithSimilarWords() {
        // 测试包含类似单词但实际安全的SQL
        String[] safeSqls = {
            "SELECT * FROM users WHERE deleted = false",
            "SELECT * FROM orders WHERE updated_at > ?",
            "SELECT * FROM products WHERE created_by = ?",
            "SELECT * FROM logs WHERE truncated_message IS NOT NULL"
        };

        for (String safeSql : safeSqls) {
            // 这些SQL包含类似的单词但应该是安全的
            String upperSql = safeSql.toUpperCase();
            boolean containsOnlyAsFieldNames = !upperSql.matches(".*\\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE)\\b.*");
            assertTrue(containsOnlyAsFieldNames, "不应该误报为危险SQL: " + safeSql);
        }
    }
}
