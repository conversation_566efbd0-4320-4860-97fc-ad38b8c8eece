package cn.facilityone.ai.controller;

import cn.facilityone.ai.dto.ReportAnalysisResult;
import cn.facilityone.ai.dto.WorkOrderAnalysisResult;
import cn.facilityone.ai.exception.AIServiceException;
import cn.facilityone.ai.exception.CSVParseException;
import cn.facilityone.ai.exception.ReportAnalysisException;
import cn.facilityone.ai.exception.UnsupportedFileFormatException;
import cn.facilityone.ai.service.biz.FileBizService;
import cn.facilityone.ai.service.biz.ReportBizService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.io.FileNotFoundException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ToolController 集成测试
 * 测试报表分析API的完整调用流程和异常处理
 */
@WebMvcTest(ToolController.class)
@DisplayName("ToolController 集成测试")
class ToolControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ReportBizService reportBizService;

    @MockBean
    private FileBizService fileBizService;

    private ReportAnalysisResult mockSuccessResult;
    private WorkOrderAnalysisResult mockStatisticalData;

    @BeforeEach
    void setUp() {
        // 准备模拟的统计数据
        mockStatisticalData = new WorkOrderAnalysisResult();
        mockStatisticalData.setTotalCount(5);
        
        Map<String, Integer> statusDistribution = new HashMap<>();
        statusDistribution.put("已完成", 3);
        statusDistribution.put("进行中", 1);
        statusDistribution.put("待分配", 1);
        mockStatisticalData.setStatusDistribution(statusDistribution);
        
        Map<String, Integer> priorityDistribution = new HashMap<>();
        priorityDistribution.put("高", 2);
        priorityDistribution.put("中", 2);
        priorityDistribution.put("低", 1);
        mockStatisticalData.setPriorityDistribution(priorityDistribution);

        // 准备模拟的分析结果
        mockSuccessResult = new ReportAnalysisResult();
        mockSuccessResult.setStatisticalData(mockStatisticalData);
        mockSuccessResult.setAiAnalysis("基于数据分析，工单处理效率良好，建议继续保持。");
        mockSuccessResult.setFileName("test-report.csv");
        mockSuccessResult.setAnalysisTime(LocalDateTime.now());
        mockSuccessResult.setDataCount(5);
        mockSuccessResult.setProcessingTimeMs(1500L);
        mockSuccessResult.setAnalysisStatus("SUCCESS");
    }

    @Test
    @DisplayName("成功分析报表 - 返回完整分析结果")
    void testAnalyzeReport_Success() throws Exception {
        // Given
        Long fileId = 1L;
        when(reportBizService.analyzeReportByFileId(fileId)).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.message").value("分析完成"))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.fileName").value("test-report.csv"))
                .andExpect(jsonPath("$.data.dataCount").value(5))
                .andExpect(jsonPath("$.data.statisticalData.totalCount").value(5))
                .andExpect(jsonPath("$.data.statisticalData.statusDistribution.已完成").value(3))
                .andExpect(jsonPath("$.data.statisticalData.priorityDistribution.高").value(2))
                .andExpect(jsonPath("$.data.aiAnalysis").value("基于数据分析，工单处理效率良好，建议继续保持。"));

        // 验证服务调用
        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("参数验证 - 文件ID为空")
    void testAnalyzeReport_NullFileId() throws Exception {
        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("文件ID不能为空且必须大于0"))
                .andExpect(jsonPath("$.data").doesNotExist());

        // 验证服务未被调用
        verify(reportBizService, never()).analyzeReportByFileId(anyLong());
    }

    @Test
    @DisplayName("参数验证 - 文件ID为负数")
    void testAnalyzeReport_NegativeFileId() throws Exception {
        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", "-1")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("文件ID不能为空且必须大于0"))
                .andExpect(jsonPath("$.data").doesNotExist());

        // 验证服务未被调用
        verify(reportBizService, never()).analyzeReportByFileId(anyLong());
    }

    @Test
    @DisplayName("参数验证 - 文件ID为零")
    void testAnalyzeReport_ZeroFileId() throws Exception {
        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", "0")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("文件ID不能为空且必须大于0"))
                .andExpect(jsonPath("$.data").doesNotExist());

        // 验证服务未被调用
        verify(reportBizService, never()).analyzeReportByFileId(anyLong());
    }

    @Test
    @DisplayName("文件不存在异常处理")
    void testAnalyzeReport_FileNotFound() throws Exception {
        // Given
        Long fileId = 999L;
        when(reportBizService.analyzeReportByFileId(fileId))
                .thenThrow(new FileNotFoundException("文件不存在"));

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("404"))
                .andExpect(jsonPath("$.message").value("文件不存在或已被删除"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("不支持的文件格式异常处理")
    void testAnalyzeReport_UnsupportedFileFormat() throws Exception {
        // Given
        Long fileId = 2L;
        UnsupportedFileFormatException exception = new UnsupportedFileFormatException(
                "test-file.txt", "text/plain", "仅支持CSV格式文件");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("不支持的文件格式，仅支持CSV文件。文件名: test-file.txt, 格式: text/plain"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("CSV解析异常处理 - 包含错误行号和列名")
    void testAnalyzeReport_CSVParseException_WithDetails() throws Exception {
        // Given
        Long fileId = 3L;
        CSVParseException exception = new CSVParseException("日期格式错误", "test-file.csv", 3, "创建日期");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("CSV文件格式错误或数据异常，错误位置：第3行，错误列：创建日期，请检查文件内容格式"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("CSV解析异常处理 - 仅包含基本错误信息")
    void testAnalyzeReport_CSVParseException_BasicError() throws Exception {
        // Given
        Long fileId = 4L;
        CSVParseException exception = new CSVParseException("文件格式错误");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("CSV文件格式错误或数据异常，请检查文件内容格式"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("AI服务异常处理 - 超时错误")
    void testAnalyzeReport_AIServiceException_Timeout() throws Exception {
        // Given
        Long fileId = 5L;
        AIServiceException exception = new AIServiceException("AI服务响应超时", "TIMEOUT_ERROR");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("AI分析服务响应超时，请稍后重试"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("AI服务异常处理 - 连接错误")
    void testAnalyzeReport_AIServiceException_Connection() throws Exception {
        // Given
        Long fileId = 6L;
        AIServiceException exception = new AIServiceException("连接失败", "CONNECTION_ERROR");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("AI分析服务连接异常，请检查网络连接或稍后重试"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("AI服务异常处理 - 未知错误码")
    void testAnalyzeReport_AIServiceException_Unknown() throws Exception {
        // Given
        Long fileId = 7L;
        AIServiceException exception = new AIServiceException("未知AI服务错误", "UNKNOWN_ERROR");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("AI分析服务异常，请稍后重试"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("报表分析异常处理 - 数据库错误")
    void testAnalyzeReport_ReportAnalysisException_Database() throws Exception {
        // Given
        Long fileId = 8L;
        ReportAnalysisException exception = new ReportAnalysisException(
                "数据库连接失败", fileId, "CSV_PARSING", "DATABASE_ERROR");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("数据库访问异常，请稍后重试"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("报表分析异常处理 - 解析阶段错误")
    void testAnalyzeReport_ReportAnalysisException_ParsePhase() throws Exception {
        // Given
        Long fileId = 9L;
        ReportAnalysisException exception = new ReportAnalysisException(
                "解析失败", fileId, "CSV_PARSING", "UNKNOWN_ERROR");
        when(reportBizService.analyzeReportByFileId(fileId)).thenThrow(exception);

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("CSV文件解析失败，请检查文件格式"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("运行时异常处理")
    void testAnalyzeReport_RuntimeException() throws Exception {
        // Given
        Long fileId = 10L;
        when(reportBizService.analyzeReportByFileId(fileId))
                .thenThrow(new RuntimeException("系统内部错误"));

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("系统内部错误，请稍后重试"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("通用异常处理")
    void testAnalyzeReport_GeneralException() throws Exception {
        // Given
        Long fileId = 11L;
        when(reportBizService.analyzeReportByFileId(fileId))
                .thenThrow(new Exception("未知异常"));

        // When & Then
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("系统发生未知错误，请联系管理员"))
                .andExpect(jsonPath("$.data").doesNotExist());

        verify(reportBizService, times(1)).analyzeReportByFileId(fileId);
    }

    @Test
    @DisplayName("HTTP方法不支持 - GET请求")
    void testAnalyzeReport_UnsupportedMethod() throws Exception {
        // When & Then
        mockMvc.perform(get("/tool/reportAnalysis")
                        .param("fileId", "1"))
                .andExpect(status().isMethodNotAllowed());

        // 验证服务未被调用
        verify(reportBizService, never()).analyzeReportByFileId(anyLong());
    }

    @Test
    @DisplayName("请求路径错误")
    void testAnalyzeReport_WrongPath() throws Exception {
        // When & Then
        mockMvc.perform(post("/tool/wrongPath")
                        .param("fileId", "1")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isNotFound());

        // 验证服务未被调用
        verify(reportBizService, never()).analyzeReportByFileId(anyLong());
    }

    @Test
    @DisplayName("并发请求处理")
    void testAnalyzeReport_ConcurrentRequests() throws Exception {
        // Given
        Long fileId1 = 13L;
        Long fileId2 = 14L;
        
        ReportAnalysisResult result1 = new ReportAnalysisResult();
        result1.setFileName("file1.csv");
        result1.setDataCount(3);
        
        ReportAnalysisResult result2 = new ReportAnalysisResult();
        result2.setFileName("file2.csv");
        result2.setDataCount(5);
        
        when(reportBizService.analyzeReportByFileId(fileId1)).thenReturn(result1);
        when(reportBizService.analyzeReportByFileId(fileId2)).thenReturn(result2);

        // When & Then - 模拟并发请求
        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId1.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.data.fileName").value("file1.csv"))
                .andExpect(jsonPath("$.data.dataCount").value(3));

        mockMvc.perform(post("/tool/reportAnalysis")
                        .param("fileId", fileId2.toString())
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.data.fileName").value("file2.csv"))
                .andExpect(jsonPath("$.data.dataCount").value(5));

        // 验证两次服务调用
        verify(reportBizService, times(1)).analyzeReportByFileId(fileId1);
        verify(reportBizService, times(1)).analyzeReportByFileId(fileId2);
    }
}