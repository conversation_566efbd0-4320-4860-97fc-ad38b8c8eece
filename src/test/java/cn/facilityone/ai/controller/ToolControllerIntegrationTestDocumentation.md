# ToolController 集成测试文档

## 测试概述

本文档描述了 ToolController 报表分析 API 的集成测试要求和测试场景。由于 Spring Boot 测试环境配置复杂，这里提供了详细的测试规范和手动测试指南。

## 测试数据文件

已创建以下测试数据文件：

### 1. 有效的CSV文件 (`src/test/resources/test-data/valid-workorder-report.csv`)
包含完整的工单数据，用于测试正常的分析流程：
- 5条工单记录
- 包含所有必需字段：工单号、申请人、创建日期时间、状态等
- 数据格式正确，可以正常解析

### 2. 无效的CSV文件 (`src/test/resources/test-data/invalid-csv-format.csv`)
包含格式错误的数据，用于测试异常处理：
- 日期格式错误
- 时间格式错误
- 字段不完整

### 3. 空文件 (`src/test/resources/test-data/empty-file.csv`)
用于测试空文件处理逻辑

### 4. 非CSV文件 (`src/test/resources/test-data/non-csv-file.txt`)
用于测试文件格式验证

## 集成测试场景

### 1. 成功场景测试

**测试目标**: 验证完整的报表分析流程
**请求**: `POST /tool/reportAnalysis?fileId=1`
**预期响应**:
```json
{
  "code": "200",
  "message": "分析完成",
  "data": {
    "fileName": "valid-workorder-report.csv",
    "dataCount": 5,
    "statisticalData": {
      "totalCount": 5,
      "statusDistribution": {
        "已完成": 3,
        "进行中": 1,
        "待分配": 1
      },
      "priorityDistribution": {
        "高": 2,
        "中": 2,
        "低": 1
      }
    },
    "aiAnalysis": "基于数据分析的AI报告...",
    "analysisTime": "2025-08-20T11:00:00",
    "processingTimeMs": 1500
  }
}
```

### 2. 参数验证测试

#### 2.1 文件ID为空
**请求**: `POST /tool/reportAnalysis`
**预期响应**:
```json
{
  "code": "400",
  "message": "文件ID不能为空且必须大于0",
  "data": null
}
```

#### 2.2 文件ID为负数
**请求**: `POST /tool/reportAnalysis?fileId=-1`
**预期响应**:
```json
{
  "code": "400",
  "message": "文件ID不能为空且必须大于0",
  "data": null
}
```

#### 2.3 文件ID为零
**请求**: `POST /tool/reportAnalysis?fileId=0`
**预期响应**:
```json
{
  "code": "400",
  "message": "文件ID不能为空且必须大于0",
  "data": null
}
```

### 3. 异常处理测试

#### 3.1 文件不存在
**请求**: `POST /tool/reportAnalysis?fileId=999`
**预期响应**:
```json
{
  "code": "404",
  "message": "文件不存在或已被删除",
  "data": null
}
```

#### 3.2 不支持的文件格式
**请求**: `POST /tool/reportAnalysis?fileId=2` (非CSV文件)
**预期响应**:
```json
{
  "code": "400",
  "message": "不支持的文件格式，仅支持CSV文件。文件名: non-csv-file.txt, 格式: text/plain",
  "data": null
}
```

#### 3.3 CSV解析异常
**请求**: `POST /tool/reportAnalysis?fileId=3` (格式错误的CSV)
**预期响应**:
```json
{
  "code": "500",
  "message": "CSV文件格式错误或数据异常，错误位置：第3行，错误列：创建日期，请检查文件内容格式",
  "data": null
}
```

#### 3.4 AI服务异常
**模拟场景**: AI服务不可用或超时
**预期响应**:
```json
{
  "code": "500",
  "message": "AI分析服务响应超时，请稍后重试",
  "data": null
}
```

### 4. HTTP方法和路径测试

#### 4.1 不支持的HTTP方法
**请求**: `GET /tool/reportAnalysis?fileId=1`
**预期响应**: HTTP 405 Method Not Allowed

#### 4.2 错误的请求路径
**请求**: `POST /tool/wrongPath?fileId=1`
**预期响应**: HTTP 404 Not Found

### 5. 并发和性能测试

#### 5.1 并发请求处理
**测试方法**: 同时发送多个不同fileId的请求
**验证点**: 
- 每个请求都能正确处理
- 响应数据不会混淆
- 系统保持稳定

#### 5.2 响应时间验证
**测试方法**: 测量API响应时间
**验证点**: 
- 正常情况下响应时间 < 30秒
- 包含AI分析的完整流程

## 手动测试指南

### 1. 环境准备
1. 启动应用程序
2. 确保数据库连接正常
3. 确保AI服务可用
4. 准备测试文件并上传到系统

### 2. 测试步骤
1. 使用Postman或curl工具发送HTTP请求
2. 验证响应状态码和响应体格式
3. 检查日志输出是否正确
4. 验证数据库状态（如有需要）

### 3. 测试工具示例

#### 使用curl测试成功场景：
```bash
curl -X POST "http://localhost:8080/tool/reportAnalysis?fileId=1" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 使用curl测试参数验证：
```bash
curl -X POST "http://localhost:8080/tool/reportAnalysis" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 自动化测试实现

由于Spring Boot测试环境配置复杂，建议采用以下方式实现自动化测试：

### 1. 单元测试
- 使用Mockito模拟依赖服务
- 测试Controller层的逻辑和异常处理
- 验证请求参数验证和响应格式

### 2. 集成测试
- 使用TestContainers启动真实的数据库
- 使用WireMock模拟外部AI服务
- 测试完整的请求-响应流程

### 3. 端到端测试
- 在测试环境中部署完整应用
- 使用真实的文件和数据进行测试
- 验证所有组件的集成效果

## 验证清单

- [ ] 成功分析有效CSV文件
- [ ] 正确处理参数验证错误
- [ ] 正确处理文件不存在异常
- [ ] 正确处理文件格式异常
- [ ] 正确处理CSV解析异常
- [ ] 正确处理AI服务异常
- [ ] 正确处理运行时异常
- [ ] 正确响应不支持的HTTP方法
- [ ] 正确响应错误的请求路径
- [ ] 支持并发请求处理
- [ ] 响应时间在合理范围内
- [ ] 日志记录完整准确
- [ ] 响应格式符合API规范

## 注意事项

1. **认证和授权**: 测试时需要提供有效的认证令牌
2. **文件上传**: 需要先上传测试文件到系统中获取fileId
3. **AI服务依赖**: 测试结果可能因AI服务状态而异
4. **数据库状态**: 某些测试可能需要特定的数据库状态
5. **环境配置**: 确保测试环境配置与生产环境一致

## 测试报告模板

```
测试日期: ____
测试环境: ____
测试人员: ____

测试结果:
- 成功场景: ✓/✗
- 参数验证: ✓/✗
- 异常处理: ✓/✗
- HTTP方法验证: ✓/✗
- 并发测试: ✓/✗
- 性能测试: ✓/✗

发现问题:
1. ____
2. ____

改进建议:
1. ____
2. ____
```