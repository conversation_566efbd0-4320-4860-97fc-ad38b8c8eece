spring:
  profiles:
    active: test
# AI服务配置 - 测试环境可以使用Mock
  ai:
    openai:
      api-key: test-api-key
      base-url: http://localhost:8080/mock-ai
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
# 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  # 数据库配置 - 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false
  
  # Redis配置 - 使用嵌入式Redis或禁用
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      database: 1

# 日志配置
logging:
  level:
    cn.facilityone.ai: DEBUG
    org.springframework.web: DEBUG
    org.springframework.test: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 测试专用配置
test:
  mock:
    ai-service: true
    file-service: true
  data:
    path: classpath:test-data/