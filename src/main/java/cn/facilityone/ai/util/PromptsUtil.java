package cn.facilityone.ai.util;

import io.micrometer.core.instrument.util.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Component
public class PromptsUtil implements ApplicationContextAware {

    private static final String PROMPTS_CACHE_KEY = "prompts:";
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext context) {
        PromptsUtil.applicationContext = context;
    }

    private static RedisTemplate<String, Object> getRedisTemplate() {
        return applicationContext.getBean("redisTemplate", RedisTemplate.class);
    }

    /**
     * 从classpath的prompts目录获取提示词md
     *
     * @param promptName 提示词名称
     * @return 提示词返回内容
     */
    public static String getPrompts(String promptName) {
        // 先从缓存获取
//        Object cachedPrompts = getRedisTemplate().opsForValue().get(PROMPTS_CACHE_KEY + promptName);
//        if (cachedPrompts != null) {
//            return (String) cachedPrompts;
//        }

        try {
            Resource resource = new PathMatchingResourcePatternResolver()
                    .getResource("classpath:prompts/" + promptName + ".md");
            String prompts = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
            // 存入缓存
//            getRedisTemplate().opsForValue().set(PROMPTS_CACHE_KEY + promptName, prompts, 1, TimeUnit.DAYS);
            return prompts;
        } catch (IOException e) {
            throw new RuntimeException("Failed to read prompts files", e);
        }
    }
}
