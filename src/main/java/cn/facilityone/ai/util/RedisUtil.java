package cn.facilityone.ai.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 * @param <K>
 * @param <V> 值类型，注意，请勿使用Long 类型，会出现转换错误
 */
@Component
public class RedisUtil<K, V> {

    @Autowired
    private RedisTemplate<K, V> redisTemplate;

    /**
     * 存储字符串键值对
     * @param key 键
     * @param value 值
     */
    public void set(K key, V value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 获取值
     * @param key 键
     * @return 值
     */
    public V get(K key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除键
     * @param key 键
     */
    public void delete(K key) {
        redisTemplate.delete(key);
    }

    /**
     * 设置带过期时间的键值对
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    public void set(K key, V value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }
}