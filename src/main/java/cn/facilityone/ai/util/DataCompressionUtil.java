package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.CompactWorkOrderAnalysisResult;
import cn.facilityone.ai.dto.OutlierData;
import cn.facilityone.ai.dto.OutlierStatistics;
import cn.facilityone.ai.dto.WorkOrderAnalysisResult;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据压缩工具类
 * 用于压缩统计数据以减少AI模型的token使用量
 */
@Slf4j
public class DataCompressionUtil {

    // Token长度限制配置
    private static final int MAX_FULL_DATA_TOKENS = 4000;  // 完整数据的token限制
    private static final int MAX_COMPACT_DATA_TOKENS = 2000; // 精简数据的token限制
    private static final int ESTIMATED_CHARS_PER_TOKEN = 4; // 估算每个token的字符数

    // 分布数据保留的最大条目数
    private static final int MAX_DISTRIBUTION_ITEMS = 5;
    private static final int MAX_PRIORITY_ITEMS = 3;
    private static final int MAX_BUILDING_ITEMS = 3;

    /**
     * 智能压缩统计数据
     * 根据数据大小自动选择合适的压缩级别
     *
     * @param fullResult 完整的统计分析结果
     * @return 压缩后的数据对象（可能是原始对象或精简版对象）
     */
    public static Object compressAnalysisResult(WorkOrderAnalysisResult fullResult) {
        if (fullResult == null) {
            return null;
        }

        // 首先尝试使用完整数据
        String fullDataJson = JSONUtil.toJsonStr(fullResult);
        int estimatedTokens = estimateTokenCount(fullDataJson);
        
        log.debug("完整数据JSON长度: {} 字符, 估算token数: {}", fullDataJson.length(), estimatedTokens);

        // 如果完整数据在限制范围内，直接返回
        if (estimatedTokens <= MAX_FULL_DATA_TOKENS) {
            log.info("使用完整数据，估算token数: {}", estimatedTokens);
            return fullResult;
        }

        // 数据过大，使用精简版
        log.info("完整数据过大({}tokens)，使用精简版数据", estimatedTokens);
        CompactWorkOrderAnalysisResult compactResult = createCompactResult(fullResult);
        
        // 验证精简版数据大小
        String compactDataJson = JSONUtil.toJsonStr(compactResult);
        int compactTokens = estimateTokenCount(compactDataJson);
        
        log.info("精简版数据JSON长度: {} 字符, 估算token数: {}", compactDataJson.length(), compactTokens);

        // 如果精简版仍然过大，进行进一步压缩
        if (compactTokens > MAX_COMPACT_DATA_TOKENS) {
            log.warn("精简版数据仍然过大({}tokens)，进行极简压缩", compactTokens);
            return createUltraCompactResult(compactResult);
        }

        return compactResult;
    }

    /**
     * 创建精简版统计结果
     *
     * @param fullResult 完整的统计分析结果
     * @return 精简版统计结果
     */
    private static CompactWorkOrderAnalysisResult createCompactResult(WorkOrderAnalysisResult fullResult) {
        CompactWorkOrderAnalysisResult compact = new CompactWorkOrderAnalysisResult();

        // 基础统计信息
        compact.setTotalCount(fullResult.getTotalCount());
        compact.setAverageProcessingTime(fullResult.getAverageProcessingTime());
        compact.setMaxProcessingTime(fullResult.getMaxProcessingTime());
        compact.setMinProcessingTime(fullResult.getMinProcessingTime());
        compact.setCompletionRate(fullResult.getCompletionRate());
        compact.setOnTimeCompletionRate(fullResult.getOnTimeCompletionRate());
        compact.setChargedOrderCount(fullResult.getChargedOrderCount());
        compact.setFreeOrderCount(fullResult.getFreeOrderCount());

        // 压缩分布数据
        compact.setTopStatusDistribution(compressDistribution(fullResult.getStatusDistribution(), MAX_DISTRIBUTION_ITEMS));
        compact.setTopPriorityDistribution(compressDistribution(fullResult.getPriorityDistribution(), MAX_PRIORITY_ITEMS));
        compact.setTopServiceTypeDistribution(compressDistribution(fullResult.getServiceTypeDistribution(), MAX_DISTRIBUTION_ITEMS));
        compact.setTopDepartmentDistribution(compressDistribution(fullResult.getDepartmentDistribution(), MAX_DISTRIBUTION_ITEMS));
        compact.setTopBuildingDistribution(compressDistribution(fullResult.getBuildingDistribution(), MAX_BUILDING_ITEMS));

        // 时间分布数据
        compact.setPeakHours(compressDistribution(fullResult.getHourlyDistribution(), 3));
        compact.setPeakMonths(compressDistribution(fullResult.getMonthlyDistribution(), 3));
        compact.setPeakWeekdays(compressDistribution(fullResult.getWeeklyDistribution(), 3));

        // 离群数据信息
        compact.setTotalOutlierCount(fullResult.getTotalOutlierCount());
        compact.setOutlierPercentage(fullResult.getOutlierPercentage());
        compact.setProcessingTimeOutlierCount(fullResult.getProcessingTimeOutlierCount());
        compact.setResponseTimeOutlierCount(fullResult.getResponseTimeOutlierCount());
        compact.setBusinessRuleOutlierCount(fullResult.getBusinessRuleOutlierCount());

        // 压缩离群数据统计（不包含具体工单对象）
        if (fullResult.getOutlierStatistics() != null) {
            compact.setOutlierTypeDistribution(convertOutlierTypeDistribution(
                    fullResult.getOutlierStatistics().getOutlierTypeDistribution()));
            compact.setOutlierSeverityDistribution(fullResult.getOutlierStatistics().getSeverityDistribution());
            compact.setOutlierSummary(createCompactOutlierSummary(fullResult));
        }

        // 生成关键指标摘要
        compact.setKeyMetrics(generateKeyMetricsSummary(fullResult));

        return compact;
    }

    /**
     * 创建极简版统计结果（当精简版仍然过大时使用）
     *
     * @param compactResult 精简版统计结果
     * @return 极简版统计结果
     */
    private static CompactWorkOrderAnalysisResult createUltraCompactResult(CompactWorkOrderAnalysisResult compactResult) {
        CompactWorkOrderAnalysisResult ultraCompact = new CompactWorkOrderAnalysisResult();

        // 只保留最核心的指标
        ultraCompact.setTotalCount(compactResult.getTotalCount());
        ultraCompact.setAverageProcessingTime(compactResult.getAverageProcessingTime());
        ultraCompact.setCompletionRate(compactResult.getCompletionRate());
        ultraCompact.setTotalOutlierCount(compactResult.getTotalOutlierCount());
        ultraCompact.setOutlierPercentage(compactResult.getOutlierPercentage());

        // 只保留前3个分布项
        ultraCompact.setTopStatusDistribution(compressDistribution(compactResult.getTopStatusDistribution(), 3));
        ultraCompact.setTopDepartmentDistribution(compressDistribution(compactResult.getTopDepartmentDistribution(), 3));

        // 保留关键指标摘要
        ultraCompact.setKeyMetrics(compactResult.getKeyMetrics());

        // 简化离群数据信息
        if (compactResult.getOutlierSummary() != null) {
            CompactWorkOrderAnalysisResult.CompactOutlierSummary simplifiedSummary = 
                    new CompactWorkOrderAnalysisResult.CompactOutlierSummary();
            simplifiedSummary.setMaxProcessingTimeOutlier(compactResult.getOutlierSummary().getMaxProcessingTimeOutlier());
            simplifiedSummary.setBusinessRuleAnomalyTypes(
                    compressDistribution(compactResult.getOutlierSummary().getBusinessRuleAnomalyTypes(), 3));
            ultraCompact.setOutlierSummary(simplifiedSummary);
        }

        return ultraCompact;
    }

    /**
     * 压缩分布数据，只保留前N个最重要的项目
     *
     * @param distribution 原始分布数据
     * @param maxItems     最大保留项目数
     * @return 压缩后的分布数据
     */
    private static Map<String, Integer> compressDistribution(Map<String, Integer> distribution, int maxItems) {
        if (CollectionUtil.isEmpty(distribution)) {
            return new HashMap<>();
        }

        return distribution.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(maxItems)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * 转换离群数据类型分布
     *
     * @param outlierTypeDistribution 原始离群类型分布
     * @return 转换后的字符串键分布
     */
    private static Map<String, Integer> convertOutlierTypeDistribution(Map<OutlierData.OutlierType, Integer> outlierTypeDistribution) {
        if (CollectionUtil.isEmpty(outlierTypeDistribution)) {
            return new HashMap<>();
        }

        return outlierTypeDistribution.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().getDescription(),
                        Map.Entry::getValue
                ));
    }

    /**
     * 创建精简版离群数据摘要（不包含工单对象）
     *
     * @param fullResult 完整的统计分析结果
     * @return 精简版离群数据摘要
     */
    private static CompactWorkOrderAnalysisResult.CompactOutlierSummary createCompactOutlierSummary(WorkOrderAnalysisResult fullResult) {
        CompactWorkOrderAnalysisResult.CompactOutlierSummary summary =
                new CompactWorkOrderAnalysisResult.CompactOutlierSummary();

        // 从离群数据统计中提取信息，避免处理完整的工单对象
        OutlierStatistics stats = fullResult.getOutlierStatistics();
        if (stats != null) {
            // 从处理时长统计中获取信息
            if (stats.getProcessingTimeStats() != null) {
                summary.setMaxProcessingTimeOutlier(stats.getProcessingTimeStats().getMaxProcessingTime());
                summary.setMinProcessingTimeOutlier(stats.getProcessingTimeStats().getMinProcessingTime());
            }

            // 从响应时间统计中获取信息
            if (stats.getResponseTimeStats() != null) {
                summary.setMaxResponseTimeOutlier(stats.getResponseTimeStats().getMaxResponseTime());
            }

            // 从业务规则统计中获取信息
            if (stats.getBusinessRuleStats() != null) {
                Map<String, Integer> businessRuleTypes = new HashMap<>();
                if (stats.getBusinessRuleStats().getStatusAnomalyCount() > 0) {
                    businessRuleTypes.put("状态异常", stats.getBusinessRuleStats().getStatusAnomalyCount());
                }
                if (stats.getBusinessRuleStats().getTimeLogicAnomalyCount() > 0) {
                    businessRuleTypes.put("时间逻辑异常", stats.getBusinessRuleStats().getTimeLogicAnomalyCount());
                }
                if (stats.getBusinessRuleStats().getDataIntegrityAnomalyCount() > 0) {
                    businessRuleTypes.put("数据完整性异常", stats.getBusinessRuleStats().getDataIntegrityAnomalyCount());
                }
                summary.setBusinessRuleAnomalyTypes(businessRuleTypes);
            }
        }

        // 分析受影响的部门（如果有部门信息）
        // 注意：为了减少token使用，这里暂时跳过部门分析
        // 在实际应用中，可以考虑在离群数据中直接存储部门信息而不是完整工单对象
        Map<String, Integer> affectedDepartments = new HashMap<>();
        summary.setTopAffectedDepartments(affectedDepartments);

        return summary;
    }

    /**
     * 生成关键指标摘要
     *
     * @param fullResult 完整的统计分析结果
     * @return 关键指标摘要
     */
    private static CompactWorkOrderAnalysisResult.KeyMetricsSummary generateKeyMetricsSummary(WorkOrderAnalysisResult fullResult) {
        CompactWorkOrderAnalysisResult.KeyMetricsSummary summary = 
                new CompactWorkOrderAnalysisResult.KeyMetricsSummary();

        // 评估工作效率等级
        if (fullResult.getAverageProcessingTime() != null) {
            if (fullResult.getAverageProcessingTime() <= 60) {
                summary.setEfficiencyLevel("高");
            } else if (fullResult.getAverageProcessingTime() <= 180) {
                summary.setEfficiencyLevel("中");
            } else {
                summary.setEfficiencyLevel("低");
            }
        }

        // 评估数据质量等级
        double outlierRate = fullResult.getOutlierPercentage() != null ? fullResult.getOutlierPercentage() : 0.0;
        if (outlierRate <= 5.0) {
            summary.setDataQualityLevel("优");
        } else if (outlierRate <= 15.0) {
            summary.setDataQualityLevel("良");
        } else {
            summary.setDataQualityLevel("差");
        }

        // 评估响应速度等级
        if (fullResult.getOnTimeCompletionRate() != null) {
            if (fullResult.getOnTimeCompletionRate() >= 90.0) {
                summary.setResponseSpeedLevel("快");
            } else if (fullResult.getOnTimeCompletionRate() >= 70.0) {
                summary.setResponseSpeedLevel("中");
            } else {
                summary.setResponseSpeedLevel("慢");
            }
        }

        // 确定主要问题类型
        if (fullResult.getProcessingTimeOutlierCount() != null && fullResult.getProcessingTimeOutlierCount() > 0) {
            summary.setPrimaryIssueType("处理效率");
        } else if (fullResult.getBusinessRuleOutlierCount() != null && fullResult.getBusinessRuleOutlierCount() > 0) {
            summary.setPrimaryIssueType("数据质量");
        } else if (fullResult.getResponseTimeOutlierCount() != null && fullResult.getResponseTimeOutlierCount() > 0) {
            summary.setPrimaryIssueType("响应速度");
        } else {
            summary.setPrimaryIssueType("整体良好");
        }

        // 生成改进优先级建议
        if ("差".equals(summary.getDataQualityLevel())) {
            summary.setImprovementPriority("优先改善数据质量");
        } else if ("低".equals(summary.getEfficiencyLevel())) {
            summary.setImprovementPriority("优先提升处理效率");
        } else if ("慢".equals(summary.getResponseSpeedLevel())) {
            summary.setImprovementPriority("优先改善响应速度");
        } else {
            summary.setImprovementPriority("保持现有水平");
        }

        return summary;
    }

    /**
     * 估算文本的token数量
     *
     * @param text 文本内容
     * @return 估算的token数量
     */
    private static int estimateTokenCount(String text) {
        if (StrUtil.isBlank(text)) {
            return 0;
        }
        return text.length() / ESTIMATED_CHARS_PER_TOKEN;
    }

    /**
     * 检查数据是否需要压缩
     *
     * @param dataJson JSON格式的数据
     * @return 是否需要压缩
     */
    public static boolean needsCompression(String dataJson) {
        int estimatedTokens = estimateTokenCount(dataJson);
        return estimatedTokens > MAX_FULL_DATA_TOKENS;
    }

    /**
     * 获取数据大小信息
     *
     * @param data 数据对象
     * @return 数据大小信息字符串
     */
    public static String getDataSizeInfo(Object data) {
        if (data == null) {
            return "数据为空";
        }
        
        String json = JSONUtil.toJsonStr(data);
        int chars = json.length();
        int tokens = estimateTokenCount(json);
        String type = data instanceof CompactWorkOrderAnalysisResult ? "精简版" : "完整版";
        
        return String.format("%s数据 - 字符数: %d, 估算token数: %d", type, chars, tokens);
    }
}
