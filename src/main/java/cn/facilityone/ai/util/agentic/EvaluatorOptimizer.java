/*
 * Copyright 2024 - 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.facilityone.ai.util.agentic;

import java.util.ArrayList;
import java.util.List;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.util.Assert;

/**
 * 工作流: <b>评估器-优化器</b>
 * <p/>
 * 实现了大语言模型(LLM)交互的评估器-优化器工作流模式。该工作流编排了一个双LLM过程，
 * 其中一个模型生成响应，另一个在迭代循环中提供评估和反馈，类似于人类作家的迭代改进过程。
 *
 * <p>
 * 该工作流由两个主要组件组成：
 * <ul>
 * <li>生成器LLM：产生初始响应并基于反馈进行改进</li>
 * <li>评估器LLM：分析响应并提供详细的改进反馈</li>
 * </ul>
 *
 * <b>使用标准</b>
 * 该工作流在满足以下条件的场景中特别有效：
 * <ul>
 * <li>存在明确的评估标准来评估响应质量</li>
 * <li>迭代改进为输出提供可衡量的价值</li>
 * <li>任务受益于多轮批评和改进</li>
 * </ul>
 *
 * <b>适用性指标</b>
 * 两个关键指标表明该工作流是合适的：
 * <ul>
 * <li>当反馈被明确表达时，LLM响应可以得到明显改善</li>
 * <li>评估器LLM能够提供实质性和可操作的反馈</li>
 * </ul>
 *
 * <b>应用示例</b>
 * <ul>
 * <li>需要通过迭代改进捕获细微差别的文学翻译</li>
 * <li>需要多轮搜索和分析的复杂搜索任务</li>
 * <li>可以通过系统性审查提高质量的代码生成</li>
 * <li>需要多次草稿和特定改进的内容创作</li>
 * </ul>
 *
 * <AUTHOR> Tzolov
 * @see <a href=
 *      "https://www.anthropic.com/research/building-effective-agents">Building
 *      effective agents</a>
 */
@SuppressWarnings("null")
public class EvaluatorOptimizer {

    public static final String DEFAULT_GENERATOR_PROMPT = """
			Your goal is to complete the task based on the input. If there are feedback
			from your previous generations, you should reflect on them to improve your solution.

			CRITICAL: Your response must be a SINGLE LINE of valid JSON with NO LINE BREAKS except those explicitly escaped with \\n.
			Here is the exact format to follow, including all quotes and braces:

			{"thoughts":"Brief description here","response":"public class Example {\\n    // Code here\\n}"}

			Rules for the response field:
			1. ALL line breaks must use \\n
			2. ALL quotes must use \\"
			3. ALL backslashes must be doubled: \\
			4. NO actual line breaks or formatting - everything on one line
			5. NO tabs or special characters
			6. Java code must be complete and properly escaped

			Example of properly formatted response:
			{"thoughts":"Implementing counter","response":"public class Counter {\\n    private int count;\\n    public Counter() {\\n        count = 0;\\n    }\\n    public void increment() {\\n        count++;\\n    }\\n}"}

			Follow this format EXACTLY - your response must be valid JSON on a single line.
			""";

    public static final String DEFAULT_EVALUATOR_PROMPT = """
			评估此代码实现的正确性、时间复杂度和最佳实践。
			确保代码有适当的javadoc文档。
			用一行中的确切JSON格式响应：

			{"evaluation":"PASS, NEEDS_IMPROVEMENT, or FAIL", "feedback":"你的反馈在这里"}

			评估字段必须是以下之一："PASS"、"NEEDS_IMPROVEMENT"、"FAIL"
			只有在满足所有标准且不需要改进时才使用"PASS"。
			""";

    /**
     * 表示解决方案生成步骤。包含模型的思考和提议的解决方案。
     *
     * @param thoughts 模型对任务和反馈的理解
     * @param response 模型提议的解决方案
     */
    public static record Generation(String thoughts, String response) {
    }

    /**
     * 表示评估响应。包含评估结果和详细反馈。
     *
     * @param evaluation 评估结果（PASS、NEEDS_IMPROVEMENT或FAIL）
     * @param feedback   改进的详细反馈
     */
    public static record EvaluationResponse(Evaluation evaluation, String feedback) {

        public enum Evaluation {
            PASS, NEEDS_IMPROVEMENT, FAIL
        }
    }

    /**
     * 表示最终精炼的响应。包含最终解决方案和显示解决方案演进的思维链。
     *
     * @param solution       最终解决方案
     * @param chainOfThought 显示解决方案演进的思维链
     */
    public static record RefinedResponse(String solution, List<Generation> chainOfThought) {
    }

    private final ChatClient chatClient;

    private final String generatorPrompt;

    private final String evaluatorPrompt;

    public EvaluatorOptimizer(ChatClient chatClient) {
        this(chatClient, DEFAULT_GENERATOR_PROMPT, DEFAULT_EVALUATOR_PROMPT);
    }

    public EvaluatorOptimizer(ChatClient chatClient, String generatorPrompt, String evaluatorPrompt) {
        Assert.notNull(chatClient, "ChatClient must not be null");
        Assert.hasText(generatorPrompt, "Generator prompt must not be empty");
        Assert.hasText(evaluatorPrompt, "Evaluator prompt must not be empty");

        this.chatClient = chatClient;
        this.generatorPrompt = generatorPrompt;
        this.evaluatorPrompt = evaluatorPrompt;
    }

    /**
     * 为给定任务启动评估器-优化器工作流。此方法编排生成和评估的迭代过程，
     * 直到达到满意的解决方案。
     *
     * <p>
     * 工作流遵循以下步骤：
     * </p>
     * <ol>
     * <li>生成初始解决方案</li>
     * <li>根据质量标准评估解决方案</li>
     * <li>如果评估通过，返回解决方案</li>
     * <li>如果评估表明需要改进，整合反馈并生成新解决方案</li>
     * <li>重复步骤2-4直到达到满意的解决方案</li>
     * </ol>
     *
     * @param task 通过迭代改进要解决的任务或问题
     * @return 包含最终解决方案和显示解决方案演进的思维链的RefinedResponse
     */
    public RefinedResponse loop(String task) {
        List<String> memory = new ArrayList<>();
        List<Generation> chainOfThought = new ArrayList<>();

        return loop(task, "", memory, chainOfThought);
    }

    /**
     * 评估器-优化器循环的内部递归实现。此方法维护先前尝试和反馈的状态，
     * 同时递归地改进解决方案，直到满足评估标准。
     *
     * @param task           要解决的原始任务
     * @param context        累积的上下文，包括先前的尝试和反馈
     * @param memory         用于参考的先前解决方案尝试列表
     * @param chainOfThought 跟踪解决方案和推理演进的列表
     * @return 包含最终解决方案和完整解决方案历史的RefinedResponse
     */
    private RefinedResponse loop(String task, String context, List<String> memory,
                                 List<Generation> chainOfThought) {

        Generation generation = generate(task, context);
        memory.add(generation.response());
        chainOfThought.add(generation);

        EvaluationResponse evaluationResponse = evalute(generation.response(), task);

        if (evaluationResponse.evaluation().equals(EvaluationResponse.Evaluation.PASS)) {
            // Solution is accepted!
            return new RefinedResponse(generation.response(), chainOfThought);
        }

        // 累积新的上下文，包括最后一次和之前的尝试以及反馈。
        StringBuilder newContext = new StringBuilder();
        newContext.append("Previous attempts:");
        for (String m : memory) {
            newContext.append("\n- ").append(m);
        }
        newContext.append("\nFeedback: ").append(evaluationResponse.feedback());

        return loop(task, newContext.toString(), memory, chainOfThought);
    }

    /**
     * 基于给定任务和反馈上下文生成或改进解决方案。此方法代表工作流的生成器组件，
     * 产生可以通过评估反馈迭代改进的响应。
     *
     * @param task    要解决的主要任务或问题
     * @param context 用于迭代改进的先前尝试和反馈
     * @return 包含模型思考和提议解决方案的Generation
     */
    private Generation generate(String task, String context) {
        Generation generationResponse = chatClient.prompt()
                .user(u -> u.text("{prompt}\n{context}\nTask: {task}")
                        .param("prompt", this.generatorPrompt)
                        .param("context", context)
                        .param("task", task))
                .call()
                .entity(Generation.class);

        System.out.println(String.format("\n=== GENERATOR OUTPUT ===\nTHOUGHTS: %s\n\nRESPONSE:\n %s\n",
                generationResponse.thoughts(), generationResponse.response()));
        return generationResponse;
    }

    /**
     * 评估解决方案是否满足指定的要求和质量标准。此方法代表工作流的评估器组件，
     * 分析解决方案并提供详细反馈以进一步改进，直到达到所需的质量水平。
     *
     * @param content 要评估的解决方案内容
     * @param task    用于评估解决方案的原始任务
     * @return 包含评估结果（PASS/NEEDS_IMPROVEMENT/FAIL）和改进详细反馈的EvaluationResponse
     */
    private EvaluationResponse evalute(String content, String task) {

        EvaluationResponse evaluationResponse = chatClient.prompt()
                .user(u -> u.text("{prompt}\nOriginal task: {task}\nContent to evaluate: {content}")
                        .param("prompt", this.evaluatorPrompt)
                        .param("task", task)
                        .param("content", content))
                .call()
                .entity(EvaluationResponse.class);

        System.out.println(String.format("\n=== EVALUATOR OUTPUT ===\nEVALUATION: %s\n\nFEEDBACK: %s\n",
                evaluationResponse.evaluation(), evaluationResponse.feedback()));
        return evaluationResponse;
    }

}