/*
 * Copyright 2024 - 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.facilityone.ai.util.agentic;

import java.util.List;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.util.Assert;

/**
 * 模式: <b>编排器-工作者</b>
 * <p/>
 * 在此模式中，中央LLM（编排器）动态地将复杂任务分解为子任务，
 * 将它们委托给工作者LLM，并使用合成器来组合它们的结果。编排器分析输入
 * 以确定需要什么子任务以及如何执行它们，而工作者专注于它们特定的分配任务。
 * 最后，合成器将工作者的输出整合为一个连贯的结果。
 * <p/>
 * 关键组件：
 * <ul>
 * <li>编排器：分析任务并确定所需子任务的中央LLM</li>
 * <li>工作者：执行特定子任务的专门LLM</li>
 * <li>合成器：将工作者输出组合为最终结果的组件</li>
 * </ul>
 * <p/>
 * 何时使用：此模式非常适合无法预先预测所需子任务的复杂任务。
 * 例如：
 * <ul>
 * <li>编码任务，其中要更改的文件数量和更改性质取决于特定请求</li>
 * <li>涉及从多个来源收集和分析信息的搜索任务</li>
 * </ul>
 * <p/>
 * 虽然在拓扑上类似于并行化，但关键区别在于其灵活性——子任务不是预定义的，
 * 而是由编排器根据特定输入动态确定的。这使得它对于需要自适应问题解决
 * 和多个专门组件之间协调的任务特别有效。
 *
 * <AUTHOR> Tzolov
 * @see <a href=
 *      "https://www.anthropic.com/research/building-effective-agents">Building
 *      effective agents</a>
 */
public class OrchestratorWorkers {

    private final ChatClient chatClient;
    private final String orchestratorPrompt;
    private final String workerPrompt;

    public static final String DEFAULT_ORCHESTRATOR_PROMPT = """
			Analyze this task and break it down into 2-3 distinct approaches:

			Task: {task}

			Return your response in this JSON format:
			\\{
			"analysis": "Explain your understanding of the task and which variations would be valuable.
			             Focus on how each approach serves different aspects of the task.",
			"tasks": [
				\\{
				"type": "formal",
				"description": "Write a precise, technical version that emphasizes specifications"
				\\},
				\\{
				"type": "conversational",
				"description": "Write an engaging, friendly version that connects with readers"
				\\}
			]
			\\}
			""";

    public static final String DEFAULT_WORKER_PROMPT = """
			Generate content based on:
			Task: {original_task}
			Style: {task_type}
			Guidelines: {task_description}
			""";

    /**
     * 表示由编排器识别的需要由工作者执行的子任务。
     *
     * @param type        任务的类型或类别（例如，"formal"、"conversational"）
     * @param description 工作者应该完成什么的详细描述
     */
    public static record Task(String type, String description) {
    }

    /**
     * 来自编排器的响应，包含任务分析和分解为子任务。
     *
     * @param analysis 任务的详细解释以及不同方法如何服务于其各个方面
     * @param tasks    编排器识别的要由工作者执行的子任务列表
     */
    public static record OrchestratorResponse(String analysis, List<Task> tasks) {
    }

    /**
     * 包含编排器分析和组合工作者输出的最终响应。
     *
     * @param analysis        编排器对原始任务的理解和分解
     * @param workerResponses 来自工作者的响应列表，每个处理特定的子任务
     */
    public static record FinalResponse(String analysis, List<String> workerResponses) {
    }

    /**
     * 使用默认提示创建新的OrchestratorWorkers。
     *
     * @param chatClient 用于LLM交互的ChatClient
     */
    public OrchestratorWorkers(ChatClient chatClient) {
        this(chatClient, DEFAULT_ORCHESTRATOR_PROMPT, DEFAULT_WORKER_PROMPT);
    }

    /**
     * 使用自定义提示创建新的OrchestratorWorkers。
     *
     * @param chatClient         用于LLM交互的ChatClient
     * @param orchestratorPrompt 编排器LLM的自定义提示
     * @param workerPrompt       工作者LLM的自定义提示
     */
    public OrchestratorWorkers(ChatClient chatClient, String orchestratorPrompt, String workerPrompt) {
        Assert.notNull(chatClient, "ChatClient must not be null");
        Assert.hasText(orchestratorPrompt, "Orchestrator prompt must not be empty");
        Assert.hasText(workerPrompt, "Worker prompt must not be empty");

        this.chatClient = chatClient;
        this.orchestratorPrompt = orchestratorPrompt;
        this.workerPrompt = workerPrompt;
    }

    /**
     * 使用编排器-工作者模式处理任务。
     * 首先，编排器分析任务并将其分解为子任务。
     * 然后，工作者并行执行每个子任务。
     * 最后，将结果组合为单个响应。
     *
     * @param taskDescription 要处理的任务描述
     * @return 包含编排器分析和组合工作者输出的WorkerResponse
     * @throws IllegalArgumentException 如果taskDescription为null或空
     */
    @SuppressWarnings("null")
    public FinalResponse process(String taskDescription) {
        Assert.hasText(taskDescription, "Task description must not be empty");

        // 步骤1：获取编排器响应
        OrchestratorResponse orchestratorResponse = this.chatClient.prompt()
                .user(u -> u.text(this.orchestratorPrompt)
                        .param("task", taskDescription))
                .call()
                .entity(OrchestratorResponse.class);

        System.out.println(String.format("\n=== ORCHESTRATOR OUTPUT ===\nANALYSIS: %s\n\nTASKS: %s\n",
                orchestratorResponse.analysis(), orchestratorResponse.tasks()));

        // 步骤2：处理每个任务
        List<String> workerResponses = orchestratorResponse.tasks().stream().map(task -> this.chatClient.prompt()
                .user(u -> u.text(this.workerPrompt)
                        .param("original_task", taskDescription)
                        .param("task_type", task.type())
                        .param("task_description", task.description()))
                .call()
                .content()).toList();

        System.out.println("\n=== WORKER OUTPUT ===\n" + workerResponses);

        return new FinalResponse(orchestratorResponse.analysis(), workerResponses);
    }

}