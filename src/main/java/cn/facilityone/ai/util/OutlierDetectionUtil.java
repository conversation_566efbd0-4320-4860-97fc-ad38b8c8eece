package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.OutlierData;
import cn.facilityone.ai.dto.WorkOrderData;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 离群数据检测工具类
 * 提供多种离群数据检测算法
 */
@Slf4j
public class OutlierDetectionUtil {

    // 默认的Z-Score阈值
    private static final double DEFAULT_Z_SCORE_THRESHOLD = 2.5;
    
    // 默认的IQR倍数
    private static final double DEFAULT_IQR_MULTIPLIER = 1.5;

    /**
     * 检测处理时长离群数据（使用IQR方法）
     *
     * @param workOrderList 工单数据列表
     * @return 离群数据列表
     */
    public static List<OutlierData> detectProcessingTimeOutliers(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        // 提取有效的处理时长数据
        List<WorkOrderData> validWorkOrders = workOrderList.stream()
                .filter(wo -> wo.getWorkDurationMinutes() != null && wo.getWorkDurationMinutes() > 0)
                .collect(Collectors.toList());

        if (validWorkOrders.size() < 4) {
            log.debug("处理时长数据不足，无法进行离群检测，有效数据量: {}", validWorkOrders.size());
            return new ArrayList<>();
        }

        // 计算IQR
        List<Integer> processingTimes = validWorkOrders.stream()
                .map(WorkOrderData::getWorkDurationMinutes)
                .sorted()
                .collect(Collectors.toList());

        double q1 = calculatePercentile(processingTimes, 25);
        double q3 = calculatePercentile(processingTimes, 75);
        double iqr = q3 - q1;
        double lowerBound = q1 - DEFAULT_IQR_MULTIPLIER * iqr;
        double upperBound = q3 + DEFAULT_IQR_MULTIPLIER * iqr;

        log.debug("处理时长IQR分析 - Q1: {}, Q3: {}, IQR: {}, 下界: {}, 上界: {}", 
                q1, q3, iqr, lowerBound, upperBound);

        // 检测离群数据
        List<OutlierData> outliers = new ArrayList<>();
        for (WorkOrderData workOrder : validWorkOrders) {
            Integer duration = workOrder.getWorkDurationMinutes();
            if (duration < lowerBound || duration > upperBound) {
                OutlierData outlier = new OutlierData();
                outlier.setWorkOrder(workOrder);
                outlier.setOutlierType(OutlierData.OutlierType.PROCESSING_TIME_OUTLIER);
                outlier.setAnomalyField("workDurationMinutes");
                outlier.setAnomalyValue(duration);
                
                // 计算离群程度
                double outlierScore = duration < lowerBound ? 
                        (lowerBound - duration) / iqr : (duration - upperBound) / iqr;
                outlier.setOutlierScore(outlierScore);
                
                // 生成原因描述
                String reason = duration < lowerBound ? 
                        String.format("处理时长过短: %d分钟 (正常范围: %.1f-%.1f分钟)", duration, lowerBound, upperBound) :
                        String.format("处理时长过长: %d分钟 (正常范围: %.1f-%.1f分钟)", duration, lowerBound, upperBound);
                outlier.setReason(reason);
                outlier.setNormalRange(String.format("%.1f-%.1f分钟", lowerBound, upperBound));
                
                outliers.add(outlier);
            }
        }

        log.info("处理时长离群检测完成，检测到 {} 个离群数据", outliers.size());
        return outliers;
    }

    /**
     * 检测响应时间离群数据
     *
     * @param workOrderList 工单数据列表
     * @return 离群数据列表
     */
    public static List<OutlierData> detectResponseTimeOutliers(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        // 计算响应时间（从创建时间到接单时间）
        List<WorkOrderData> validWorkOrders = workOrderList.stream()
                .filter(wo -> wo.getCreateTime() != null && wo.getAcceptTime() != null)
                .collect(Collectors.toList());

        if (validWorkOrders.size() < 4) {
            log.debug("响应时间数据不足，无法进行离群检测，有效数据量: {}", validWorkOrders.size());
            return new ArrayList<>();
        }

        // 计算响应时间（分钟）
        List<Long> responseTimes = validWorkOrders.stream()
                .map(wo -> ChronoUnit.MINUTES.between(wo.getCreateTime(), wo.getAcceptTime()))
                .filter(time -> time >= 0) // 过滤负值
                .sorted()
                .collect(Collectors.toList());

        if (responseTimes.size() < 4) {
            return new ArrayList<>();
        }

        // 使用Z-Score方法检测离群数据
        double mean = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        double stdDev = calculateStandardDeviation(responseTimes, mean);

        List<OutlierData> outliers = new ArrayList<>();
        for (WorkOrderData workOrder : validWorkOrders) {
            long responseTime = ChronoUnit.MINUTES.between(workOrder.getCreateTime(), workOrder.getAcceptTime());
            if (responseTime >= 0) {
                double zScore = Math.abs((responseTime - mean) / stdDev);
                if (zScore > DEFAULT_Z_SCORE_THRESHOLD) {
                    OutlierData outlier = new OutlierData();
                    outlier.setWorkOrder(workOrder);
                    outlier.setOutlierType(OutlierData.OutlierType.RESPONSE_TIME_OUTLIER);
                    outlier.setAnomalyField("responseTime");
                    outlier.setAnomalyValue(responseTime);
                    outlier.setOutlierScore(zScore);
                    
                    String reason = String.format("响应时间异常: %d分钟 (平均: %.1f分钟, 标准差: %.1f)", 
                            responseTime, mean, stdDev);
                    outlier.setReason(reason);
                    outlier.setNormalRange(String.format("%.1f±%.1f分钟", mean, stdDev * DEFAULT_Z_SCORE_THRESHOLD));
                    
                    outliers.add(outlier);
                }
            }
        }

        log.info("响应时间离群检测完成，检测到 {} 个离群数据", outliers.size());
        return outliers;
    }

    /**
     * 检测业务规则离群数据
     *
     * @param workOrderList 工单数据列表
     * @return 离群数据列表
     */
    public static List<OutlierData> detectBusinessRuleOutliers(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        List<OutlierData> outliers = new ArrayList<>();

        for (WorkOrderData workOrder : workOrderList) {
            // 检测时间逻辑异常
            outliers.addAll(detectTimeLogicAnomalies(workOrder));
            
            // 检测数据完整性异常
            outliers.addAll(detectDataIntegrityAnomalies(workOrder));
            
            // 检测状态异常
            outliers.addAll(detectStatusAnomalies(workOrder));
        }

        log.info("业务规则离群检测完成，检测到 {} 个离群数据", outliers.size());
        return outliers;
    }

    /**
     * 计算百分位数
     */
    private static double calculatePercentile(List<Integer> sortedData, double percentile) {
        if (sortedData.isEmpty()) {
            return 0.0;
        }
        
        double index = (percentile / 100.0) * (sortedData.size() - 1);
        int lowerIndex = (int) Math.floor(index);
        int upperIndex = (int) Math.ceil(index);
        
        if (lowerIndex == upperIndex) {
            return sortedData.get(lowerIndex);
        }
        
        double weight = index - lowerIndex;
        return sortedData.get(lowerIndex) * (1 - weight) + sortedData.get(upperIndex) * weight;
    }

    /**
     * 计算标准差
     */
    private static double calculateStandardDeviation(List<Long> data, double mean) {
        if (data.size() <= 1) {
            return 0.0;
        }
        
        double sumSquaredDiffs = data.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .sum();
        
        return Math.sqrt(sumSquaredDiffs / (data.size() - 1));
    }

    /**
     * 检测时间逻辑异常
     */
    private static List<OutlierData> detectTimeLogicAnomalies(WorkOrderData workOrder) {
        List<OutlierData> outliers = new ArrayList<>();

        // 检测实际开始时间晚于实际结束时间
        if (workOrder.getActualStartTime() != null && workOrder.getActualEndTime() != null) {
            if (workOrder.getActualStartTime().isAfter(workOrder.getActualEndTime())) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder, 
                        "timeLogic", "实际开始时间晚于实际结束时间",
                        "时间逻辑错误：开始时间不能晚于结束时间");
                outliers.add(outlier);
            }
        }

        // 检测预估时间逻辑异常
        if (workOrder.getEstimatedStartTime() != null && workOrder.getEstimatedEndTime() != null) {
            if (workOrder.getEstimatedStartTime().isAfter(workOrder.getEstimatedEndTime())) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "timeLogic", "预估开始时间晚于预估结束时间",
                        "时间逻辑错误：预估开始时间不能晚于预估结束时间");
                outliers.add(outlier);
            }
        }

        return outliers;
    }

    /**
     * 检测数据完整性异常
     */
    private static List<OutlierData> detectDataIntegrityAnomalies(WorkOrderData workOrder) {
        List<OutlierData> outliers = new ArrayList<>();

        // 检测关键字段缺失
        if (StrUtil.isBlank(workOrder.getOrderNumber())) {
            OutlierData outlier = createBusinessRuleOutlier(workOrder,
                    "dataIntegrity", "工单号缺失",
                    "数据完整性错误：工单号不能为空");
            outliers.add(outlier);
        }

        if (StrUtil.isBlank(workOrder.getApplicant())) {
            OutlierData outlier = createBusinessRuleOutlier(workOrder,
                    "dataIntegrity", "申请人缺失",
                    "数据完整性错误：申请人不能为空");
            outliers.add(outlier);
        }

        // 检测工作时长与实际时间不匹配
        if (workOrder.getWorkDurationMinutes() != null && 
            workOrder.getActualStartTime() != null && 
            workOrder.getActualEndTime() != null) {
            
            long actualDuration = ChronoUnit.MINUTES.between(
                    workOrder.getActualStartTime(), workOrder.getActualEndTime());
            
            if (Math.abs(actualDuration - workOrder.getWorkDurationMinutes()) > 30) { // 允许30分钟误差
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "dataIntegrity", "工作时长与实际时间不匹配",
                        String.format("数据一致性错误：记录的工作时长(%d分钟)与实际时间差异过大(实际%d分钟)", 
                                workOrder.getWorkDurationMinutes(), actualDuration));
                outliers.add(outlier);
            }
        }

        return outliers;
    }

    /**
     * 检测状态异常
     */
    private static List<OutlierData> detectStatusAnomalies(WorkOrderData workOrder) {
        List<OutlierData> outliers = new ArrayList<>();

        // 检测状态与时间字段的一致性
        if (StrUtil.isNotBlank(workOrder.getStatus())) {
            String status = workOrder.getStatus().toLowerCase();
            
            // 如果状态是已完成，但没有实际结束时间
            if ((status.contains("完成") || status.contains("结束") || status.contains("关闭")) 
                && workOrder.getActualEndTime() == null) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "statusAnomaly", "已完成状态但无结束时间",
                        "状态异常：工单状态为已完成但缺少实际结束时间");
                outliers.add(outlier);
            }
            
            // 如果状态是进行中，但有结束时间
            if (status.contains("进行") && workOrder.getActualEndTime() != null) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "statusAnomaly", "进行中状态但有结束时间",
                        "状态异常：工单状态为进行中但已有实际结束时间");
                outliers.add(outlier);
            }
        }

        return outliers;
    }

    /**
     * 创建业务规则离群数据对象
     */
    private static OutlierData createBusinessRuleOutlier(WorkOrderData workOrder, 
                                                        String anomalyField, 
                                                        Object anomalyValue, 
                                                        String reason) {
        OutlierData outlier = new OutlierData();
        outlier.setWorkOrder(workOrder);
        outlier.setOutlierType(OutlierData.OutlierType.BUSINESS_RULE_OUTLIER);
        outlier.setAnomalyField(anomalyField);
        outlier.setAnomalyValue(anomalyValue);
        outlier.setReason(reason);
        outlier.setOutlierScore(1.0); // 业务规则异常统一设为1.0
        return outlier;
    }
}
