package cn.facilityone.ai.util;

import cn.facilityone.ai.entity.FileEntity;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Iterator;

@Slf4j
public class ImageUtil {

    /**
     * 最大文件大小，单位KB
     */
    private static final long MAX_FILE_SIZE = 512L;

    /**
     * 压缩后的图片文件格式
     */
    public static final String COMPRESSED_IMAGE_FORMAT = "jpg";

    /**
     * 将图片压缩到720p（1280x720），保持原比例
     *
     * @param file 原始图片文件
     * @return 压缩后的文件
     */
    public static File compressImageTo720p(FileEntity file, String fileUploadPath) {
        if (file == null || !FileEntity.FileType.IMAGE.equals(file.getType())) {
            log.error("未传入有效的图片对象或者不是图像文件，文件对象：{}", JSONUtil.toJsonStr(file));
            return null;
        }
        String path = fileUploadPath + File.separator + file.getPath();
        File imageFile = new File(path);
        if (!imageFile.exists()) {
            log.error("图像文件不存在，文件对象：{}", JSONUtil.toJsonStr(file));
            return null;
        }

        try {
            // 读取原始图片
            BufferedImage originalImage = ImageIO.read(imageFile);
            if (originalImage == null) {
                log.error("无法读取图片文件：{}", path);
                return null;
            }

            // 计算720p的尺寸，保持原比例
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            float aspectRatio = (float) originalWidth / originalHeight;

            int targetWidth, targetHeight;
            if (aspectRatio > (16.0f / 9.0f)) {
                // 如果原图更宽，以高度为基准
                targetHeight = 720;
                targetWidth = Math.round(targetHeight * aspectRatio);
            } else {
                // 如果原图更高，以宽度为基准
                targetWidth = 1280;
                targetHeight = Math.round(targetWidth / aspectRatio);
            }

            // 调整图片大小
            BufferedImage resizedImage = resizeImage(originalImage, targetWidth, targetHeight);

            // 创建压缩后的文件
            String compressedPath = path.substring(0, path.lastIndexOf(".")) + "_720p." +
                    path.substring(path.lastIndexOf(".") + 1);
            File compressedFile = new File(compressedPath);

            // 压缩图片
            float quality = 1.0f;
            do {
                if (compressedFile.exists()) {
                    compressedFile.delete();
                }

//                String formatName = path.substring(path.lastIndexOf(".") + 1).toLowerCase();
                String formatName = COMPRESSED_IMAGE_FORMAT;
                if ("jpg".equals(formatName) || "jpeg".equals(formatName)) {
                    compressJPEG(resizedImage, compressedFile, quality);
                } else {
                    ImageIO.write(resizedImage, formatName, compressedFile);
                }

                // 检查文件大小
                if (compressedFile.length() <= 1024 * MAX_FILE_SIZE) { // 1MB
                    break;
                }

                // 降低质量继续压缩
                quality -= 0.1f;
            } while (quality > 0.1f);

            // 如果最终文件仍然超过大小限制，返回null
            if (compressedFile.length() > 1024 * MAX_FILE_SIZE) {
                log.error("无法将图片压缩到指定大小:{}kb", MAX_FILE_SIZE);
                compressedFile.delete();
                return null;
            }

            return compressedFile;
        } catch (IOException e) {
            log.error("压缩图片失败：", e);
            return null;
        }
    }

    /**
     * 调整图片大小
     */
    private static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 如果图片已经小于目标尺寸，直接返回
        if (originalWidth <= targetWidth && originalHeight <= targetHeight) {
            return originalImage;
        }

        // 创建新的图片
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
        Graphics2D g = resizedImage.createGraphics();

        // 设置图片质量
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制调整后的图片
        g.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g.dispose();

        return resizedImage;
    }

    /**
     * 压缩JPEG图片
     */
    private static void compressJPEG(BufferedImage image, File output, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
        if (!writers.hasNext()) {
            throw new IOException("No JPEG writer available");
        }

        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(quality);

        try (FileOutputStream fos = new FileOutputStream(output);
             ImageOutputStream ios = ImageIO.createImageOutputStream(fos)) {
            writer.setOutput(ios);
            writer.write(null, new IIOImage(image, null, null), param);
        } finally {
            writer.dispose();
        }
    }
}
