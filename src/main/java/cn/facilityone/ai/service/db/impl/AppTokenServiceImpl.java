package cn.facilityone.ai.service.db.impl;

import cn.facilityone.ai.entity.AppTokenEntity;
import cn.facilityone.ai.mapper.AppTokenMapper;
import cn.facilityone.ai.service.db.AppTokenService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 应用Token表Service实现类
 */
@Service
public class AppTokenServiceImpl extends ServiceImpl<AppTokenMapper, AppTokenEntity> implements AppTokenService {
    @Override
    public AppTokenEntity findValidToken(String token) {
        return lambdaQuery()
                .eq(AppTokenEntity::getToken, token)
                .eq(AppTokenEntity::getStatus, 1)
                .and(q -> q.isNull(AppTokenEntity::getExpiresAt).or().gt(AppTokenEntity::getExpiresAt, java.time.LocalDateTime.now()))
                .one();
    }
}