package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.service.db.FileService;
import cn.facilityone.ai.service.tool.FmBaseTool;
import cn.facilityone.ai.util.ImageUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class FileBizService {

    @Value("${app.file.upload-path:}")
    private String uploadPath;

    @Value("${app.file.temp-path:}")
    private String tempPath;

    private final FileService fileService;
    private final FmBaseTool fmBaseTool;

    @Autowired
    public FileBizService(FileService fileService, FmBaseTool fmBaseTool) {
        this.fileService = fileService;
        this.fmBaseTool = fmBaseTool;
    }

    public FileEntity uploadFile(MultipartFile file) {
        try {
            // 获取上传用户信息
            ApiAuthenticationToken authentication =
                    (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
            ContentInfo contentInfo = authentication.getContentInfo();

            // 先读取文件内容到字节数组，避免多次读取 inputStream 导致流耗尽
            byte[] fileBytes = file.getBytes();
            // 计算文件哈希值
            String hash = DigestUtil.sha256Hex(fileBytes);

            // 检查是否已存在相同hash的文件，存在则返回之前值
            FileEntity existFile = fileService.lambdaQuery()
                    .eq(FileEntity::getHash, hash)
                    .eq(FileEntity::getUserId, contentInfo.getUserId())
                    .eq(FileEntity::getAppId, contentInfo.getAppId())
                    .one();
            if (existFile != null) {
                return existFile;
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = UUID.randomUUID() + (extension.isEmpty() ? ".tmp" : extension);

            // 获取当前日期路径（年/月/日）
            String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            // 拼接完整上传目录
            File uploadDirectory = new File(this.uploadPath + File.separator + datePath);
            if (!uploadDirectory.exists()) {
                uploadDirectory.mkdirs();
            }
            Path uploadPath = Paths.get(uploadDirectory.getAbsolutePath(), filename);

            // 保存文件（用字节数组写入）
            Files.write(uploadPath, fileBytes);

            // 判断文件类型
            FileEntity.FileType fileType = getFileType(originalFilename);
//            FileEntity.FileType fileType = getFileTypeViaTika(fileBytes, originalFilename);
            // 保存图片信息到数据库
            FileEntity fileEntity = new FileEntity();
            fileEntity.setName(originalFilename);
            fileEntity.setHash(hash);
            fileEntity.setAppId(contentInfo.getAppId());
            fileEntity.setUserId(contentInfo.getUserId());
            fileEntity.setType(fileType);
            fileEntity.setUploadTime(new Date());
            // 只保存相对路径
            String relativePath = datePath + File.separator + filename;
            fileEntity.setPath(relativePath);
            fileService.save(fileEntity);
            return fileEntity;
        } catch (IOException e) {
            throw new RuntimeException("上传文件失败，错误信息：", e);
        }
    }

    public List<FileEntity> uploadMultipleFile(MultipartFile[] fileList) {
        List<FileEntity> fileEntityList = new ArrayList<>(fileList.length);
        for (MultipartFile multipartFile : fileList) {
            FileEntity fileEntity = uploadFile(multipartFile);
            fileEntityList.add(fileEntity);
        }
        return fileEntityList;
    }

    /**
     * 上传微信文件
     *
     * @param mediaIdList 微信媒体文件ID
     * @param customerKey 微信公众号标识key
     * @return 文件实体
     * @throws RuntimeException 当文件下载或处理失败时抛出
     */
    public List<FileEntity> uploadWeChatFile(List<String> mediaIdList, String customerKey) {
        if (CollectionUtil.isEmpty(mediaIdList) || StrUtil.isBlank(customerKey)) {
            throw new IllegalArgumentException("微信媒体ID和客户标识不能为空");
        }
        
        log.info("开始下载微信文件，mediaIdList: {}, customerKey: {}", JSONUtil.toJsonStr(mediaIdList), customerKey);
        
        try {
            // 1. 获取微信accessToken
            String accessToken = fmBaseTool.getWechatAccessToken(customerKey);
            if (StrUtil.isBlank(accessToken)) {
                throw new RuntimeException("获取微信accessToken失败");
            }

            // 2. 调用微信接口下载文件
            List<FileEntity> fileEntityList = new ArrayList<>();
            for (String mediaId : mediaIdList) {
                String weChatUrl = String.format("https://api.weixin.qq.com/cgi-bin/media/get?access_token=%s&media_id=%s",
                        accessToken, mediaId);

                FileEntity fileEntity = downloadAndProcessWechatFile(weChatUrl, mediaId);
                fileEntityList.add(fileEntity);
            }
            return fileEntityList;
        } catch (IOException e) {
            log.error("微信文件处理失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信文件处理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 下载并处理微信文件
     * @param weChatUrl 微信文件下载URL
     * @param mediaId 微信媒体ID（用于日志和标识）
     * @return 处理后的文件实体
     * @throws IOException 当IO操作失败时抛出
     */
    private FileEntity downloadAndProcessWechatFile(String weChatUrl, String mediaId) throws IOException {
        byte[] fileBytes;
        String fileName = "wechat_file_" + mediaId; // 默认文件名包含mediaId以便追踪
        
        // 设置请求超时
        try (HttpResponse response = HttpRequest.get(weChatUrl)
                .timeout(30000) // 30秒超时
                .execute()) {
            
            if (!response.isOk()) {
                String errorMsg = String.format("微信文件下载失败，HTTP状态码: %d, 响应内容: %s", 
                        response.getStatus(), response.body());
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
            
            // 获取文件名（从Content-Disposition头或默认命名）
            String contentDisposition = response.header("Content-Disposition");
            if (contentDisposition != null && contentDisposition.contains("filename=")) {
                try {
                    fileName = contentDisposition.split("filename=")[1].replaceAll("[\"'\\r\\n]", "");
                    // 确保文件名有效
                    if (StrUtil.isBlank(fileName)) {
                        fileName = "wechat_file_" + mediaId;
                    }
                } catch (Exception e) {
                    log.warn("解析Content-Disposition头失败: {}, 使用默认文件名", e.getMessage());
                }
            }
            
            fileBytes = response.bodyBytes();
            
            if (fileBytes == null || fileBytes.length == 0) {
                throw new RuntimeException("微信文件下载失败，文件数据为空");
            }
            
            log.info("微信文件下载成功，mediaId: {}, 文件大小: {} 字节, 文件名: {}", 
                    mediaId, fileBytes.length, fileName);
        }

        // 创建MultipartFile包装类
        final String finalFileName = fileName;
        final byte[] finalFileBytes = fileBytes;
        
        MultipartFile multipartFile = new MultipartFile() {
            @Override
            public String getName() {
                return mediaId;
            }

            @Override
            public String getOriginalFilename() {
                return finalFileName;
            }

            @Override
            public String getContentType() {
                // 尝试根据文件扩展名推断内容类型
                String extension = "";
                if (finalFileName.contains(".")) {
                    extension = finalFileName.substring(finalFileName.lastIndexOf(".") + 1).toLowerCase();
                }
                
                return switch (extension) {
                    case "jpg", "jpeg" -> "image/jpeg";
                    case "png" -> "image/png";
                    case "gif" -> "image/gif";
                    case "mp3" -> "audio/mpeg";
                    case "wav" -> "audio/wav";
                    case "mp4" -> "video/mp4";
                    case "pdf" -> "application/pdf";
                    default -> null; // 让系统自动判断
                };
            }

            @Override
            public boolean isEmpty() {
                return finalFileBytes == null || finalFileBytes.length == 0;
            }

            @Override
            public long getSize() {
                return finalFileBytes != null ? finalFileBytes.length : 0;
            }

            @Override
            public byte[] getBytes() {
                return finalFileBytes;
            }

            @Override
            public InputStream getInputStream() {
                return new ByteArrayInputStream(finalFileBytes);
            }

            @Override
            public void transferTo(File dest) throws IOException {
                try (FileOutputStream fos = new FileOutputStream(dest)) {
                    fos.write(finalFileBytes);
                    fos.flush();
                }
            }
        };

        // 使用现有逻辑保存文件
        return uploadFile(multipartFile);
    }


    private static FileEntity.FileType getFileType(String originalFilename) {
        FileEntity.FileType fileType = FileEntity.FileType.OTHER;
        if (StrUtil.isBlank(originalFilename)) {
            return fileType;
        }
        String[] split = originalFilename.split("\\.");
        if (split.length > 1) {
            String type = split[split.length - 1].toLowerCase();
            fileType = switch (type) {
                case "png", "jpg", "jpeg" -> FileEntity.FileType.IMAGE;
                case "wav", "mp3" -> FileEntity.FileType.AUDIO;
                case "mp4" -> FileEntity.FileType.VIDEO;
                case "pdf", "md", "txt", "json", "docx" -> FileEntity.FileType.DOCUMENT;
                default -> FileEntity.FileType.OTHER;
            };
        }
        return fileType;
    }

    public FileEntity compressImage(FileEntity originFile) {
        // 查询是否是已有压缩后的文件
        FileEntity existFile = fileService.lambdaQuery()
                .eq(FileEntity::getParentFileId, originFile.getId())
                .eq(FileEntity::getAppId, originFile.getAppId())
                .eq(FileEntity::getUserId, originFile.getUserId())
                .eq(FileEntity::getIsThumb, true)
                .one();
        if (existFile != null) {
            return existFile;
        }

        File compressedFile = ImageUtil.compressImageTo720p(originFile, uploadPath);
        if (compressedFile == null) {
            log.error("图片压缩失败");
            return null;
        }

        // 获取上传用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        // 计算文件哈希值
        String hash = DigestUtil.sha256Hex(compressedFile);
        // 文件类型
        FileEntity.FileType fileType = FileEntity.FileType.IMAGE;
        // 获取当前日期路径（年/月/日）
        String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        // 保存图片信息到数据库
        FileEntity fileEntity = new FileEntity();
        fileEntity.setName(compressedFile.getName());
        fileEntity.setHash(hash);
        fileEntity.setAppId(contentInfo.getAppId());
        fileEntity.setUserId(contentInfo.getUserId());
        fileEntity.setType(fileType);
        fileEntity.setUploadTime(new Date());
        fileEntity.setParentFileId(originFile.getId());
        fileEntity.setIsThumb(true);
        // 只保存相对路径
        String relativePath = datePath + File.separator + compressedFile.getName();
        fileEntity.setPath(relativePath);
        fileService.save(fileEntity);
        return fileEntity;
    }

    public File getFile(FileEntity fileEntity) {
        String path = uploadPath + File.separator + fileEntity.getPath();
        return new File(path);
    }
    
    /**
     * 获取临时文件
     * 
     * @param relativePath 文件相对路径
     * @return 临时文件对象
     * @throws RuntimeException 当文件不存在时抛出
     */
    public File getTempFile(String relativePath) {
        if (StrUtil.isBlank(relativePath)) {
            throw new RuntimeException("文件路径不能为空");
        }
        
        // 构建完整路径
        File file = new File(tempPath + File.separator + relativePath);
        
        // 检查文件是否存在
        if (!file.exists()) {
            log.error("临时文件不存在: {}", relativePath);
            throw new RuntimeException("临时文件不存在或已被删除");
        }
        
        return file;
    }
    
    /**
     * 上传临时文件
     * 
     * @param outputStream 输出流，用于写入文件内容
     * @param fileName 文件名称
     * @return 临时文件相对路径
     * @throws RuntimeException 当文件上传失败时抛出
     */
    public String uploadTempFile(OutputStream outputStream, String fileName) {
        try {
            // 确保文件名不为空
            if (StrUtil.isBlank(fileName)) {
                fileName = "unknown_file";
            }
            
            // 获取文件内容字节数组
            byte[] fileBytes;
            if (outputStream instanceof ByteArrayOutputStream byteArrayOutputStream) {
                fileBytes = byteArrayOutputStream.toByteArray();
            } else {
                throw new IllegalArgumentException("不支持的输出流类型");
            }
            
            // 计算文件哈希值作为文件名
            String hash = DigestUtil.sha256Hex(fileBytes);
            
            // 获取文件扩展名
            String extension = "";
            if (fileName.contains(".")) {
                extension = fileName.substring(fileName.lastIndexOf("."));
            }
            
            // 获取当前日期路径（年/月/日）
            String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            
            // 确保临时目录存在
            File tempDirectory = new File(this.tempPath + File.separator + datePath);
            if (!tempDirectory.exists()) {
                tempDirectory.mkdirs();
            }
            
            // 创建文件名（使用hash值）
            String tempFileName = hash + (extension.isEmpty() ? ".tmp" : extension);
            File tempFile = new File(tempDirectory, tempFileName);
            
            // 如果文件已存在（相同hash），直接返回路径
            if (tempFile.exists()) {
                log.info("临时文件已存在，直接返回路径: {}", datePath + File.separator + tempFileName);
                return datePath + File.separator + tempFileName;
            }
            
            // 创建文件并写入内容
            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(fileBytes);
                fileOutputStream.flush();
            }
            
            log.info("临时文件上传成功: {}", datePath + File.separator + tempFileName);
            return datePath + File.separator + tempFileName;
        } catch (IOException e) {
            log.error("上传临时文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传临时文件失败，错误信息：", e);
        }
    }

}
