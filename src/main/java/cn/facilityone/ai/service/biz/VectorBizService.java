package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.entity.AppVectorDocumentEntity;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.service.db.AppVectorDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.ExtractedTextFormatter;
import org.springframework.ai.reader.JsonReader;
import org.springframework.ai.reader.TextReader;
import org.springframework.ai.reader.markdown.MarkdownDocumentReader;
import org.springframework.ai.reader.markdown.config.MarkdownDocumentReaderConfig;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.reader.pdf.ParagraphPdfDocumentReader;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.springframework.ai.vectorstore.filter.Filter.ExpressionType.AND;

@Slf4j
@Service
public class VectorBizService {

    @Value("${spring.ai.vectorstore.pgvector.max-document-batch-size:}")
    private Integer MAX_DOCUMENT_BATCH_SIZE;

    private final FileBizService fileBizService;
    private final VectorStore vectorStore;
    private final AppVectorDocumentService appVectorDocumentService;

    @Autowired
    public VectorBizService(FileBizService fileBizService, AppVectorDocumentService appVectorDocumentService,
                            VectorStore vectorStore) {
        this.fileBizService = fileBizService;
        this.vectorStore = vectorStore;
        this.appVectorDocumentService = appVectorDocumentService;
    }

    public List<Document> similaritySearch(String query) {
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        // 过滤条件，当前应用且当前租户或默认租户的公共数据
        var b = new FilterExpressionBuilder();
        var exp = b.and(
                b.eq("appId", contentInfo.getAppId()),
                b.or(b.eq("tenantId", contentInfo.getTenantId()), b.eq("tenantId", "common"))
        ).build();

        return vectorStore.similaritySearch(SearchRequest.builder()
                .query(query)
                .filterExpression(exp)
                .topK(5)
                .build());
    }

    @Transactional(rollbackFor = Exception.class)
    public void addDocument(FileEntity fileEntity) {
        if (fileEntity == null) {
            throw new IllegalArgumentException("文件不存在");
        }
        if (fileEntity.getType() != FileEntity.FileType.DOCUMENT) {
            throw new IllegalArgumentException("文件不是文档类型");
        }
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        if (!Objects.equals(fileEntity.getAppId(), contentInfo.getAppId())) {
            throw new IllegalArgumentException("文件不存在");
        }
        Long appId = contentInfo.getAppId();
        String tenantId = contentInfo.getTenantId();
        Long fileId = fileEntity.getId();

        // 文档分类型处理
        File file = fileBizService.getFile(fileEntity);
        Resource fileResource = new FileSystemResource(file);
        List<Document> documents = Lists.newArrayList();
        if (fileEntity.getName().toLowerCase().endsWith(".pdf")) {
            // 有大纲的优先按大纲解析，无大纲的按页解析
            try {
                ParagraphPdfDocumentReader paragraphPdfDocumentReader = new ParagraphPdfDocumentReader(fileResource);
                documents = paragraphPdfDocumentReader.get();
            } catch (Exception e) {
                log.info("pdf按大纲解析失败，转为按页解析");
                PagePdfDocumentReader pagePdfDocumentReader = new PagePdfDocumentReader(fileResource);
                documents = pagePdfDocumentReader.get();
            }
        } else if (fileEntity.getName().toLowerCase().endsWith(".json")) {
            JsonReader jsonReader = new JsonReader(fileResource);
            documents = jsonReader.get();
        } else if (fileEntity.getName().toLowerCase().endsWith(".md")) {
            MarkdownDocumentReaderConfig readerConfig = MarkdownDocumentReaderConfig.defaultConfig();
            MarkdownDocumentReader markdownDocumentReader = new MarkdownDocumentReader(fileResource, readerConfig);
            documents = markdownDocumentReader.get();
        } else if (fileEntity.getName().toLowerCase().endsWith(".txt")) {
            TextReader textReader = new TextReader(fileResource);
            documents = textReader.get();
        } else {
            // 其它类型默认使用Tika（eg:docx）
            try {
                ExtractedTextFormatter extractedTextFormatter = new ExtractedTextFormatter.Builder().withLeftAlignment(true).build();
                TikaDocumentReader tikaReader =
                        new TikaDocumentReader(fileResource, extractedTextFormatter);
                documents = tikaReader.get();
            } catch (Exception e) {
                log.error("无法解析文档: {}", e.getMessage());
            }
        }
        // 分批处理入库
        if (!documents.isEmpty()) {
            // 数据处理：1、添加meta信息；2、按tokens数进行分块
            Map<String, Object> metaMap = Map.of("fileId", fileId, "appId", appId, "tenantId", tenantId);
            for (Document document : documents) {
                Map<String, Object> metadata = document.getMetadata();
                metadata.putAll(metaMap);
            }
            TokenTextSplitter tokenTextSplitter = new TokenTextSplitter(2048, 1024, 5, 10000, true);
            documents = tokenTextSplitter.apply(documents);
            try {
                for (int i = 0; i < documents.size(); i += MAX_DOCUMENT_BATCH_SIZE) {
                    int end = Math.min(i + MAX_DOCUMENT_BATCH_SIZE, documents.size());
                    List<Document> subList = new ArrayList<>(documents.subList(i, end));
                    vectorStore.add(subList);
                }
            } catch (Exception e) {
                log.error("批量插入文档时发生异常：", e);
                throw e;
            }
        }

        // 保存关联关系
        List<AppVectorDocumentEntity> appVectorDocumentEntityList = new ArrayList<>(documents.size());
        for (Document document : documents) {
            AppVectorDocumentEntity appVectorDocumentEntity = new AppVectorDocumentEntity();
            appVectorDocumentEntity.setAppId(contentInfo.getAppId());
            appVectorDocumentEntity.setFileId(fileEntity.getId());
            appVectorDocumentEntity.setVectorId(document.getId());
            appVectorDocumentEntityList.add(appVectorDocumentEntity);
        }
        appVectorDocumentService.saveBatch(appVectorDocumentEntityList);
    }
}