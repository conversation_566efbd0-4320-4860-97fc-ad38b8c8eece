package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.entity.AdminUserEntity;
import cn.facilityone.ai.service.db.AdminUserService;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class AdminUserBizService implements UserDetailsService {

    private final AdminUserService adminUserService;

    public AdminUserBizService(AdminUserService adminUserService) {
        this.adminUserService = adminUserService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        AdminUserEntity adminUser = adminUserService.getOne(new LambdaQueryWrapper<AdminUserEntity>().eq(AdminUserEntity::getUsername, username));
        if (adminUser == null) {
            throw new UsernameNotFoundException("用户不存在");
        }

        return new User(adminUser.getUsername(),
                adminUser.getPassword(),
                adminUser.getStatus(),
                true,
                true,
                true,
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_ADMIN")));
    }

    public boolean createAdminUser(String username, String password) {
        AdminUserEntity adminUser = new AdminUserEntity();
        adminUser.setUsername(username);
        adminUser.setPassword(BCrypt.hashpw(password));
        return adminUserService.save(adminUser);
    }
}