package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.constant.FmFlowConstant;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.SuggestedInfo;
import cn.facilityone.ai.entity.SuggestedQuestion;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ChatBizService {

    /**
     * 获取开场白和建议问题
     *
     * @param conversationId 会话ID
     * @return 开场白和建议问题
     */
    public SuggestedInfo getSuggestQuestion(String conversationId) {
        SuggestedInfo suggestedInfo = new SuggestedInfo();
        // 获取当前应用信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        // 未开始对话，则获取开场白和预设问题
        if (StrUtil.isBlank(conversationId) || "null".equals(conversationId) || "0".equals(conversationId)) {
            String appCode = contentInfo.getAppCode();
            if ("fm-mix-fm-app".equals(appCode)) {
                String suggestion = """
                        **您好，我是天枢AI智能助手**
                        我可以回答一些系统基础操作问题，还可以为您提供以下服务：
                        """;
                SuggestedQuestion openingStatement = new SuggestedQuestion(suggestion, SuggestedQuestion.QuestionFormat.MARKDOWN);
                suggestedInfo.setOpeningStatement(openingStatement);
                String text = FmFlowConstant.OPENING_REMARKS_QUICK_OPERATION;
                suggestedInfo.setSuggestedQuestions(List.of(
                        new SuggestedQuestion(text, SuggestedQuestion.QuestionFormat.APP_TEMPLATE)
                ));
            }
        } else {
            // 获取历史对话总结并预测出后续问题
            // TODO 2025/6/12
        }

        return suggestedInfo;
    }
}
