package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.dto.MessageDTO;
import cn.facilityone.ai.dto.UserConversationDTO;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.MessageEntity;
import cn.facilityone.ai.entity.UserConversationEntity;
import cn.facilityone.ai.service.db.MessageService;
import cn.facilityone.ai.service.db.UserConversationService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConversationBizService {

    @Autowired
    private UserConversationService userConversationService;

    @Autowired
    private MessageService messageService;

    /**
     * 获取用户的对话列表(一个月内)
     */
    public List<UserConversationDTO> getConversationList() {
        // 获取用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        String tenantId = contentInfo.getTenantId();

        // 获取偏移后的时间(一个月内)
        LocalDateTime dateTime = LocalDateTimeUtil.offset(LocalDateTimeUtil.now(), -1, ChronoUnit.MONTHS);

        List<UserConversationEntity> list = userConversationService.list(new LambdaQueryWrapper<UserConversationEntity>()
                .eq(UserConversationEntity::getUserId, contentInfo.getUserId())
                .eq(UserConversationEntity::getTenantId, tenantId)
                .eq(UserConversationEntity::getDeleted, false)
                .gt(UserConversationEntity::getCreatedAt, dateTime)
                .orderByDesc(UserConversationEntity::getId)
        );
        return list.stream().map(userConversationEntity -> {
            UserConversationDTO conversationDTO = new UserConversationDTO();
            BeanUtil.copyProperties(userConversationEntity, conversationDTO);
            return conversationDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取指定对话信息
     */
    public UserConversationDTO getConversationInfo(String conversationId) {
        // 获取用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        UserConversationEntity userConversationEntity = userConversationService.getOne(new LambdaQueryWrapper<UserConversationEntity>()
                .eq(UserConversationEntity::getUserId, contentInfo.getUserId())
                .eq(UserConversationEntity::getConversationId, conversationId)
                .eq(UserConversationEntity::getDeleted, false)
                .orderByDesc(UserConversationEntity::getId)
        );
        if (userConversationEntity == null) {
            throw new IllegalArgumentException("对话不存在");
        }
        UserConversationDTO conversationDTO = new UserConversationDTO();
        BeanUtil.copyProperties(userConversationEntity, conversationDTO);
        return conversationDTO;
    }

    /**
     * 获取历史消息
     */
    public List<MessageDTO> getHistoryMessages(String conversationId) {
        // 先获取会话ID
        LambdaQueryWrapper<UserConversationEntity> conversationWrapper = new LambdaQueryWrapper<>();
        conversationWrapper.eq(UserConversationEntity::getConversationId, conversationId);
        UserConversationEntity conversation = userConversationService.getOne(conversationWrapper);

        if (conversation == null) {
            return List.of();
        }

        LambdaQueryWrapper<MessageEntity> messageWrapper = new LambdaQueryWrapper<>();
        messageWrapper.eq(MessageEntity::getConversationId, conversation.getId())
                .orderByDesc(MessageEntity::getSequenceNumber);

        List<MessageEntity> list = messageService.list(messageWrapper);
        return list.stream().map(messageEntity -> {
            MessageDTO messageDTO = new MessageDTO();
            BeanUtils.copyProperties(messageEntity, messageDTO);
            messageDTO.setConversationId(conversationId);
            return messageDTO;
        }).toList();
    }

    public boolean deleteConversation(String conversationId) {
        // 获取对话用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        return userConversationService.update(new LambdaUpdateWrapper<UserConversationEntity>()
                .set(UserConversationEntity::getDeleted, true)
                .eq(UserConversationEntity::getConversationId, conversationId)
                .eq(UserConversationEntity::getUserId, contentInfo.getUserId())
        );
    }
}
