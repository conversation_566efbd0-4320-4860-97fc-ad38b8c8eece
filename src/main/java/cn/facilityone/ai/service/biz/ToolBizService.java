package cn.facilityone.ai.service.biz;

import cn.facilityone.ai.config.log.LogParam;
import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.service.db.FileService;
import cn.facilityone.ai.util.ImageUtil;
import cn.facilityone.ai.util.PromptsUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.dashscope.audio.asr.recognition.Recognition;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.PathResource;
import org.springframework.core.io.Resource;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/14 09:23
 */

@Slf4j
@Service
public class ToolBizService {

    @Value("${spring.ai.openai.api-key:}")
    private String apiKey;

    @Value("${app.file.upload-path:}")
    private String fileUploadPath;

    @Autowired
    private @Qualifier("vlClient") ChatClient vlClient;

    @Autowired
    private FileBizService fileBizService;
    @Autowired
    private FileService fileService;


    @LogParam(printReturn = true)
    public String getCurrentTime() {
        return DateUtil.now();
    }

    @LogParam(printReturn = true)
    public String audio2Text(FileEntity file) {
        if (file == null || !FileEntity.FileType.AUDIO.equals(file.getType())) {
            log.error("未传入有效的图片对象或者不是音频文件，文件对象：{}", JSONUtil.toJsonStr(file));
            return null;
        }
        String path = fileUploadPath + File.separator + file.getPath();
        File audioFile = new File(path);
        if (!audioFile.exists()) {
            log.error("音频文件不存在，文件对象：{}", JSONUtil.toJsonStr(file));
            return null;
        }
        // 创建Recognition实例
        Recognition recognizer = new Recognition();
        // 创建RecognitionParam
        RecognitionParam param =
                RecognitionParam.builder()
                        .apiKey(apiKey)
                        .model("paraformer-realtime-v2")
                        .format("mp3")
                        .sampleRate(16000)
                        // "language_hints"只支持paraformer-v2和paraformer-realtime-v2模型
                        .parameter("language_hints", new String[]{"zh", "en"})
                        .build();
        String text = "";
        try {
            String responseText = recognizer.call(param, audioFile);
            log.info("阿里云api解析语音返回值: {}", responseText);
            JSONObject jsonObject = JSONUtil.parseObj(responseText);
            JSONArray sentences = jsonObject.getJSONArray("sentences");
            if (sentences != null && !sentences.isEmpty()) {
                JSONObject sentence = JSONUtil.parseObj(sentences.getFirst());
                text = sentence.getStr("text");
            }
        } catch (Exception e) {
            log.error("调用阿里云api解析语音失败: ", e);
        }
        return text;
    }

    @LogParam(printReturn = true)
    public String image2Text(FileEntity file, String type) {
        // 压缩图片到720p
        File compressedFile = ImageUtil.compressImageTo720p(file, fileUploadPath);
        if (compressedFile == null) {
            log.error("压缩图片失败，无法转文字");
            return null;
        }

        // 转为resource
        Resource resource = new PathResource(compressedFile.toURI());

        String prompt = switch (type) {
            case "repair-create" -> PromptsUtil.getPrompts("fm/vl-repair-create");
            case "repair-complete" -> PromptsUtil.getPrompts("fm/vl-repair-complete");
            default -> PromptsUtil.getPrompts("common/vl");
        };

        // 判断是查询需求单还是提交需求单
        return vlClient.prompt()
                .user(u -> u.text(prompt)
                        .media(MimeTypeUtils.IMAGE_JPEG, resource))
                .call().content();
    }

    public String uploadedImage2Text(List<Long> fileIds, String type) {
        // 获取上传用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        List<FileEntity> fileList = fileService.list(new LambdaQueryWrapper<FileEntity>()
                .in(FileEntity::getId, fileIds)
                .eq(FileEntity::getAppId, contentInfo.getAppId())
                .eq(FileEntity::getUserId, contentInfo.getUserId())
        );
        if (fileList.isEmpty()) {
            throw new IllegalArgumentException("文件不存在");
        }
        return multipleImages2Text(fileList, type);
    }

    @LogParam(tag = LogParam.TAG.METHOD)
    public String multipleImages2Text(List<FileEntity> files, String type) {
        // 压缩图片到720p
        List<File> compressedFileList = new ArrayList<>(files.size());
        Set<Long> compressedFileIdSet = new HashSet<>(files.size());
        for (FileEntity file : files) {
            FileEntity compressedFileEntity = fileBizService.compressImage(file);
            compressedFileIdSet.add(compressedFileEntity.getId());
            File compressedFile = fileBizService.getFile(compressedFileEntity);
            compressedFileList.add(compressedFile);
        }
        log.info("图片转文字，传入的压缩图片ID如下：{}", JSONUtil.toJsonStr(compressedFileIdSet));
        // 转为resource
        List<Resource> resourceList = new ArrayList<>(files.size());
        for (File file : compressedFileList) {
            resourceList.add(new PathResource(file.toURI()));
        }

        String prompt;
        try {
            prompt = switch (type) {
                case "repair-create" -> PromptsUtil.getPrompts("fm/vl-repair-create");
                case "repair-complete" -> PromptsUtil.getPrompts("fm/vl-repair-complete");
                default -> PromptsUtil.getPrompts("common/vl");
            };
        } catch (Exception e) {
            log.error("获取prompt失败，使用默认prompt", e);
            prompt = PromptsUtil.getPrompts("common/vl");
        }

        // 判断是查询需求单还是提交需求单
        String finalPrompt = prompt;
        ChatClient.ChatClientRequestSpec clientRequestSpec = vlClient.prompt().user(u -> u.text(finalPrompt));
        resourceList.forEach(resource -> clientRequestSpec.user(u -> u.media(MimeTypeUtils.IMAGE_JPEG, resource)));
        return clientRequestSpec.call().content();
    }

}

