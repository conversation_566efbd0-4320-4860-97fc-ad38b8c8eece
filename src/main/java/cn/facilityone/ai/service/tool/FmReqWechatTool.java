package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.config.log.LogParam;
import cn.facilityone.ai.dto.FmCreateReqDTO;
import cn.facilityone.ai.dto.FmSearchWechatReqOrderDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:17
 */

@Slf4j
@Service
public class FmReqWechatTool {

    @Resource
    private FmReqTool fmReqTool;

    @LogParam(tag = LogParam.TAG.TOOL)
    @Tool(name = "searchWechatReqOrders", description = "搜索微信需求单，微信用户(openId)场景使用。")
    public String searchWechatReqOrders(FmSearchWechatReqOrderDTO fmSearchWechatReqOrderDTO) {
        return fmReqTool.searchWechatReqOrders(fmSearchWechatReqOrderDTO);
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    @Tool(name = "createReq", description = "创建需求单")
    public String createReq(FmCreateReqDTO fmCreateReqDTO) {
        return fmReqTool.createReqWithWechatFormat(fmCreateReqDTO);
    }
}

