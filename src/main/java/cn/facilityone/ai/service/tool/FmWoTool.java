package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.config.log.LogParam;
import cn.facilityone.ai.dto.FmCreateWoDTO;
import cn.facilityone.ai.dto.FmSearchWoOrderDTO;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:17
 */

@Slf4j
@Service
public class FmWoTool {

    @LogParam
    public List<Map<String, Object>> getServiceType(Long projectId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("projectId", projectId);
        try {
            String result = FmBaseTool.fmHttpUtil("/ai/wo/getServiceType", JSONUtil.toJsonStr(params), Method.POST);
            if (StrUtil.isBlank(result)) {
                return Collections.emptyList();
            }
            JSONObject jsonObject = JSONUtil.parseObj(result);

            // 验证必要字段是否存在
            if (!jsonObject.containsKey("code") || !jsonObject.containsKey("data")) {
                log.warn("工具getServiceType返回缺少必要字段");
                return Collections.emptyList();
            }

            String code = jsonObject.getStr("code");
            if (!"200".equals(code)) {
                return Collections.emptyList();
            }

            JSONArray dataArray = jsonObject.getJSONArray("data");
            List<Map<String, Object>> reqTypeList = new ArrayList<>();
            for (Object obj : dataArray) {
                if (obj instanceof Map) {
                    reqTypeList.add((Map<String, Object>) obj);
                } else {
                    log.warn("工具getServiceType返回数据中包含非Map类型元素：{}", obj);
                }
            }
            return reqTypeList;

        } catch (Exception e) {
            log.error("工具getServiceType调用过程中发生异常", e);
            return Collections.emptyList();
        }
    }

    @LogParam
    @Tool(name = "getPriority", description = "获取工单优先级和流程ID")
    public List<Map<String, Object>> getPriority(Long projectId, Long serviceTypeId) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("projectId", projectId);
        params.put("serviceTypeId", serviceTypeId);
        params.put("type", "ONDEMAND");
        try {
            String result = FmBaseTool.fmHttpUtil("/ai/wo/getPriority", JSONUtil.toJsonStr(params), Method.POST);
            if (StrUtil.isBlank(result)) {
                return Collections.emptyList();
            }
            JSONObject jsonObject = JSONUtil.parseObj(result);

            // 验证必要字段是否存在
            if (!jsonObject.containsKey("code") || !jsonObject.containsKey("data")) {
                log.warn("工具getPriority返回缺少必要字段");
                return Collections.emptyList();
            }

            String code = jsonObject.getStr("code");
            if (!"200".equals(code)) {
                return Collections.emptyList();
            }

            JSONArray dataArray = jsonObject.getJSONArray("data");
            List<Map<String, Object>> reqTypeList = new ArrayList<>();
            for (Object obj : dataArray) {
                if (obj instanceof Map) {
                    reqTypeList.add((Map<String, Object>) obj);
                } else {
                    log.warn("工具getPriority返回数据中包含非Map类型元素：{}", obj);
                }
            }
            return reqTypeList;

        } catch (Exception e) {
            log.error("工具getPriority调用过程中发生异常", e);
            return Collections.emptyList();
        }
    }

    @LogParam
    @Tool(name = "searchWoOrders", description = "搜索工单")
    public String searchWoOrders(FmSearchWoOrderDTO fmSearchWoOrderDTO) {
        if (fmSearchWoOrderDTO == null || fmSearchWoOrderDTO.getProjectId() == null || fmSearchWoOrderDTO.getUserId() == null) {
            return "参数错误，缺少当前操作用户的userId和项目ID";
        }
        return FmBaseTool.fmHttpUtil("/ai/wo/workorders/table", JSONUtil.toJsonStr(fmSearchWoOrderDTO), Method.POST);
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    public String createWo(FmCreateWoDTO fmCreateWoDTO) {
        if (StrUtil.isBlank(fmCreateWoDTO.getName()) && StrUtil.isBlank(fmCreateWoDTO.getPhone())) {
            return "请补充工单人姓名及联系方式";
        } else {
            if (StrUtil.isBlank(fmCreateWoDTO.getName())) {
                return "请补充工单人姓名";
            }
            if (StrUtil.isBlank(fmCreateWoDTO.getPhone())) {
                return "请补充工单人联系方式";
            }
        }
        if (fmCreateWoDTO.getServiceTypeId() == null) {
            return "请补充服务类型";
        }
        if (fmCreateWoDTO.getPriorityId() == null || fmCreateWoDTO.getWoProcessId() == 0) {
            return "请补充优先级";
        }

        String model = """
                """;

        // 参数处理
        String location = "";
        if (fmCreateWoDTO.getBuildingId() != null && fmCreateWoDTO.getBuildingId() > 0) {
            location = fmCreateWoDTO.getBuildingName();
        }
        if (fmCreateWoDTO.getFloorId() != null && fmCreateWoDTO.getFloorId() > 0) {
            location += " / " + fmCreateWoDTO.getFloorName();
        }
        if (fmCreateWoDTO.getRoomId() != null && fmCreateWoDTO.getRoomId() > 0) {
            location += " / " + fmCreateWoDTO.getRoomName();
        }
        if (fmCreateWoDTO.getRoomId() == null) {
            fmCreateWoDTO.setRoomName("");
            fmCreateWoDTO.setRoomId(0L);
        }
        if (fmCreateWoDTO.getFloorId() == null) {
            fmCreateWoDTO.setFloorName("");
            fmCreateWoDTO.setFloorId(0L);
        }
        if (fmCreateWoDTO.getBuildingId() == null) {
            fmCreateWoDTO.setBuildingName("");
            fmCreateWoDTO.setBuildingId(0L);
        }

        return StrUtil.format(model);
    }

}

