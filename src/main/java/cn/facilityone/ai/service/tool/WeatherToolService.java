package cn.facilityone.ai.service.tool;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 15:49
 */

@Service
public class WeatherToolService {
    @Tool(name = "getCurrentWeather", description = "查询当前天气")
    public String getCurrentWeather(String message) {
        System.out.println(message);
        if (message.contains("北京")) {
            return "16度";
        } else {
            return "17度";
        }
    }

        @Tool(name = "getRandomNumber", description = "获取随机数")
        public Integer getRandomNumber() {
            return (int) (Math.random() * 100);
        }
    }

