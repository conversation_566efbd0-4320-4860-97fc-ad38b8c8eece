package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.config.log.LogParam;
import cn.facilityone.ai.service.biz.FileBizService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.xy.XYSeries;
import org.jfree.data.xy.XYSeriesCollection;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.Map;

/**
 * 图表工具类，基于JFreeChart生成各种类型的图表
 * 返回图表URL，供LLM使用
 */
@Slf4j
@Component
public class ChartTool {

    // 增大默认图表尺寸
    private static final int DEFAULT_WIDTH = 1000;
    private static final int DEFAULT_HEIGHT = 750;

    // 默认字体大小
    private static final int DEFAULT_TITLE_FONT_SIZE = 20;
    private static final int DEFAULT_LABEL_FONT_SIZE = 16;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    private final FileBizService fileBizService;

    @Autowired
    public ChartTool(FileBizService fileBizService) {
        this.fileBizService = fileBizService;
    }

    /**
     * 通用图表生成方法，根据图表类型生成对应的图表
     *
     * @param chartType  图表类型（bar, pie, line, scatter）
     * @param title      图表标题
     * @param xAxisLabel X轴标签（饼图不需要）
     * @param yAxisLabel Y轴标签（饼图不需要）
     * @param data       数据集合
     * @return 图表URL
     */
    public String generateChart(String chartType, String title, String xAxisLabel,
                                String yAxisLabel, Map<?, Number> data) {
        return generateChart(chartType, title, xAxisLabel, yAxisLabel, data, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 通用图表生成方法（自定义尺寸）
     *
     * @param chartType  图表类型（bar, pie, line, scatter）
     * @param title      图表标题
     * @param xAxisLabel X轴标签
     * @param yAxisLabel Y轴标签
     * @param data       数据集合
     * @param width      图片宽度
     * @param height     图片高度
     * @return 图表URL
     */
    @SuppressWarnings("unchecked")
    @LogParam(tag = LogParam.TAG.TOOL, printReturn = true)
    @Tool(name = "generateChart", description = "生成JFreeChart图表并返回图表URL。支持四种图表类型：bar(柱状图)、pie(饼图)、line(折线图)、scatter(散点图)。参数详细说明：chartType(必填,string)-图表类型，可选值：bar/pie/line/scatter；title(必填,string)-图表标题；xAxisLabel(选填,string)-X轴标签，饼图不需要；yAxisLabel(选填,string)-Y轴标签，饼图不需要；data(必填,Map)-数据集合，对于bar和pie图key为字符串，对于line和scatter图key必须为数字；width(选填,Integer)-图片宽度，默认1000；height(选填,Integer)-图片高度，默认750")
    public String generateChart(String chartType, String title, String xAxisLabel,
                                String yAxisLabel, Map<?, Number> data, Integer width, Integer height) {
        // 处理可选参数的默认值
        int actualWidth = (width != null) ? width : DEFAULT_WIDTH;
        int actualHeight = (height != null) ? height : DEFAULT_HEIGHT;

        return generateChartInternal(chartType, title, xAxisLabel, yAxisLabel, data, actualWidth, actualHeight);
    }

    /**
     * 内部图表生成方法
     */
    private String generateChartInternal(String chartType, String title, String xAxisLabel,
                                         String yAxisLabel, Map<?, Number> data, int width, int height) {
        try {
            // 确保数据不为空
            if (data == null || data.isEmpty()) {
                throw new IllegalArgumentException("图表数据不能为空");
            }

            // 根据图表类型创建对应的图表
            JFreeChart chart = switch (chartType.toLowerCase()) {
                case "bar" -> {
                    // 将Map<?, Number>转换为Map<String, Number>
                    Map<String, Number> barData = convertToStringKeyMap(data);
                    yield createBarChart(title, xAxisLabel, yAxisLabel, barData);
                }
                case "pie" -> {
                    // 将Map<?, Number>转换为Map<String, Number>
                    Map<String, Number> pieData = convertToStringKeyMap(data);
                    yield createPieChart(title, pieData);
                }
                case "line" -> {
                    // 将Map<?, Number>转换为Map<Number, Number>
                    Map<Number, Number> lineData = convertToNumberKeyMap(data);
                    yield createLineChart(title, xAxisLabel, yAxisLabel, lineData);
                }
                case "scatter" -> {
                    // 将Map<?, Number>转换为Map<Number, Number>
                    Map<Number, Number> scatterData = convertToNumberKeyMap(data);
                    yield createScatterChart(title, xAxisLabel, yAxisLabel, scatterData);
                }
                default -> throw new IllegalArgumentException("不支持的图表类型: " + chartType);
            };

            // 设置中文字体，根据图表尺寸自动调整字体大小
            setChineseFont(chart, width, height);

            return chartToUrl(chart, width, height);
        } catch (Exception e) {
            // 记录详细错误信息
            log.error("生成图表失败: " + e.getMessage(), e);
            throw new RuntimeException("生成图表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将Map<?, Number>转换为Map<String, Number>
     */
    @SuppressWarnings("unchecked")
    private Map<String, Number> convertToStringKeyMap(Map<?, Number> data) {
        Map<String, Number> result = new java.util.HashMap<>();
        for (Map.Entry<?, Number> entry : data.entrySet()) {
            Object key = entry.getKey();
            String stringKey = (key != null) ? key.toString() : "null";
            result.put(stringKey, entry.getValue());
        }
        return result;
    }

    /**
     * 将Map<?, Number>转换为Map<Number, Number>
     */
    @SuppressWarnings("unchecked")
    private Map<Number, Number> convertToNumberKeyMap(Map<?, Number> data) {
        Map<Number, Number> result = new java.util.HashMap<>();
        for (Map.Entry<?, Number> entry : data.entrySet()) {
            Object key = entry.getKey();
            if (key instanceof Number) {
                result.put((Number) key, entry.getValue());
            } else if (key != null) {
                // 尝试将字符串转换为数字
                try {
                    Number numericKey;
                    String keyStr = key.toString();
                    if (keyStr.contains(".")) {
                        numericKey = Double.parseDouble(keyStr);
                    } else {
                        numericKey = Integer.parseInt(keyStr);
                    }
                    result.put(numericKey, entry.getValue());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("折线图和散点图的键必须是数字类型，但收到了: " + key);
                }
            }
        }
        return result;
    }

    // 定义一组柔和美观的推荐颜色
    private static final Color[] RECOMMENDED_COLORS = {
            new Color(100, 181, 246),  // 浅蓝色
            new Color(129, 199, 132),  // 浅绿色
            new Color(239, 154, 154),  // 浅红色
            new Color(159, 168, 218),  // 淡紫色
            new Color(255, 183, 77),   // 浅橙色
            new Color(128, 203, 196),  // 浅青色
            new Color(188, 170, 164),  // 浅棕色
            new Color(186, 104, 200),  // 淡紫红色
            new Color(121, 134, 203),  // 淡靛蓝色
            new Color(149, 117, 205),  // 淡紫罗兰色
            new Color(174, 213, 129),  // 淡黄绿色
            new Color(255, 204, 128)   // 淡黄色
    };

    /**
     * 创建柱状图
     */
    private static JFreeChart createBarChart(String title, String xAxisLabel, String yAxisLabel, Map<String, Number> data) {
        // 检查是否为时间段数据（如"0:00-1:00"格式）
        boolean isTimeRangeData = isTimeRangeData(data.keySet());

        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        // 获取排序后的键列表
        java.util.List<String> sortedKeys = sortDataByDate(new java.util.ArrayList<>(data.keySet()));

        // 添加数据到数据集
        for (String key : sortedKeys) {
            dataset.addValue(data.get(key), "数据", key);
        }

        JFreeChart chart = ChartFactory.createBarChart(
                title,
                xAxisLabel,
                yAxisLabel,
                dataset,
                PlotOrientation.VERTICAL,
                true,
                true,
                false
        );

        // 优化柱状图X轴标签显示
        org.jfree.chart.plot.CategoryPlot plot = chart.getCategoryPlot();
        org.jfree.chart.axis.CategoryAxis domainAxis = plot.getDomainAxis();

        // 设置X轴标签倾斜显示，提高可读性
        domainAxis.setCategoryLabelPositions(
                org.jfree.chart.axis.CategoryLabelPositions.createUpRotationLabelPositions(Math.PI / 6.0)
        );

        // 增加标签之间的间距
        domainAxis.setMaximumCategoryLabelWidthRatio(0.8f);

        // 设置随机美观颜色
        setRandomRecommendedColor(plot);

        return chart;
    }

    /**
     * 智能排序数据键，支持多种日期和时间格式
     */
    private static java.util.List<String> sortDataByDate(java.util.List<String> keys) {
        if (keys.isEmpty()) {
            return keys;
        }

        log.debug("开始排序数据键: {}", keys);

        // 检查是否为时间段数据（如"0:00-1:00"格式）
        if (isTimeRangeData(new java.util.HashSet<>(keys))) {
            log.debug("识别为时间段数据");
            return sortTimeRangeKeys(new java.util.HashSet<>(keys));
        }
        // 检查是否为月份数据（如"2025-06"格式）
        else if (isMonthData(keys)) {
            log.debug("识别为月份数据");
            java.util.List<String> sortedKeys = new java.util.ArrayList<>(keys);
            sortMonthKeys(sortedKeys);
            log.debug("月份数据排序后: {}", sortedKeys);
            return sortedKeys;
        }
        // 检查是否为星期数据
        else if (containsWeekdayData(keys)) {
            log.debug("识别为星期数据");
            java.util.List<String> sortedKeys = new java.util.ArrayList<>(keys);
            sortWeekdayKeys(sortedKeys);
            return sortedKeys;
        }
        // 检查是否为工单状态数据
        else if (containsWorkOrderStatusData(keys)) {
            log.debug("识别为工单状态数据");
            java.util.List<String> sortedKeys = new java.util.ArrayList<>(keys);
            sortWorkOrderStatusKeys(sortedKeys);
            return sortedKeys;
        }
        // 检查是否为完整日期格式（如"2025-01-15"）
        else if (isFullDateData(keys)) {
            log.debug("识别为完整日期数据");
            java.util.List<String> sortedKeys = new java.util.ArrayList<>(keys);
            sortFullDateKeys(sortedKeys);
            return sortedKeys;
        } else {
            log.debug("使用默认字符串排序");
            // 对于其他数据，尝试进行自然排序
            java.util.List<String> sortedKeys = new java.util.ArrayList<>(keys);
            try {
                java.util.Collections.sort(sortedKeys);
            } catch (Exception e) {
                // 如果排序失败，使用原始顺序
            }
            log.debug("默认排序后: {}", sortedKeys);
            return sortedKeys;
        }
    }

    /**
     * 判断是否为时间段数据（如"0:00-1:00"格式）
     */
    private static boolean isTimeRangeData(java.util.Set<String> keys) {
        if (keys.isEmpty()) {
            return false;
        }

        // 检查第一个键是否符合时间段格式
        String firstKey = keys.iterator().next();
        boolean isTimeRange = firstKey.matches("\\d+:\\d+-\\d+:\\d+") || // 匹配 "0:00-1:00" 格式
                firstKey.matches("\\d+:\\d+-\\d+") ||      // 匹配 "0:00-1" 格式
                (firstKey.matches("\\d+-\\d+") && !firstKey.matches("\\d{4}-\\d{1,2}")); // 匹配 "0-1" 格式，但排除年月格式
        
        log.debug("时间段数据判断: firstKey={}, result={}", firstKey, isTimeRange);
        return isTimeRange;
    }

    /**
     * 判断是否为完整日期数据（如"2025-01-15"格式）
     */
    private static boolean isFullDateData(java.util.List<String> keys) {
        if (keys.isEmpty()) {
            return false;
        }

        // 检查是否大部分键符合完整日期格式
        int matchCount = 0;
        for (String key : keys) {
            // 匹配 "2025-01-15" 或 "2025-1-5" 格式，必须有3个部分（年-月-日）
            if (key.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
                matchCount++;
            }
        }

        boolean isFullDate = matchCount > keys.size() / 2;
        log.debug("完整日期数据判断: keys={}, matchCount={}, threshold={}, result={}", 
                keys, matchCount, keys.size() / 2, isFullDate);

        // 如果超过一半的键符合格式，则认为是完整日期数据
        return isFullDate;
    }

    /**
     * 对完整日期格式的键进行排序
     */
    private static void sortFullDateKeys(java.util.List<String> keys) {
        keys.sort((key1, key2) -> {
            try {
                // 提取年月日信息
                String[] parts1 = key1.split("-");
                String[] parts2 = key2.split("-");

                if (parts1.length >= 3 && parts2.length >= 3) {
                    int year1 = Integer.parseInt(parts1[0]);
                    int year2 = Integer.parseInt(parts2[0]);

                    if (year1 != year2) {
                        return Integer.compare(year1, year2);
                    }

                    int month1 = Integer.parseInt(parts1[1]);
                    int month2 = Integer.parseInt(parts2[1]);

                    if (month1 != month2) {
                        return Integer.compare(month1, month2);
                    }

                    int day1 = Integer.parseInt(parts1[2]);
                    int day2 = Integer.parseInt(parts2[2]);

                    return Integer.compare(day1, day2);
                }
            } catch (NumberFormatException e) {
                // 解析失败时记录错误
                log.error("完整日期排序错误: {}, key1={}, key2={}", e.getMessage(), key1, key2);
            }

            // 如果解析失败，使用字符串比较
            return key1.compareTo(key2);
        });
    }

    /**
     * 对时间段键进行排序
     */
    private static java.util.List<String> sortTimeRangeKeys(java.util.Set<String> keys) {
        java.util.List<String> sortedKeys = new java.util.ArrayList<>(keys);

        // 根据时间段的起始时间进行排序
        sortedKeys.sort(new java.util.Comparator<>() {
            @Override
            public int compare(String key1, String key2) {
                // 提取起始时间
                int startHour1 = extractStartHour(key1);
                int startHour2 = extractStartHour(key2);

                return Integer.compare(startHour1, startHour2);
            }

            // 从时间段字符串中提取起始小时
            private int extractStartHour(String timeRange) {
                try {
                    // 处理不同格式的时间段
                    if (timeRange.contains(":")) {
                        // 处理 "0:00-1:00" 格式
                        return Integer.parseInt(timeRange.split(":")[0]);
                    } else {
                        // 处理 "0-1" 格式
                        return Integer.parseInt(timeRange.split("-")[0]);
                    }
                } catch (Exception e) {
                    return 0; // 解析失败时返回0
                }
            }
        });

        return sortedKeys;
    }

    /**
     * 创建饼图
     */
    private static JFreeChart createPieChart(String title, Map<String, Number> data) {
        DefaultPieDataset dataset = new DefaultPieDataset();

        // 使用智能排序方法对键进行排序
        java.util.List<String> sortedKeys = sortDataByDate(new java.util.ArrayList<>(data.keySet()));

        for (String key : sortedKeys) {
            dataset.setValue(key, data.get(key));
        }

        JFreeChart chart = ChartFactory.createPieChart(
                title,
                dataset,
                true,
                true,
                false
        );

        // 设置饼图使用推荐的颜色方案
        org.jfree.chart.plot.PiePlot plot = (org.jfree.chart.plot.PiePlot) chart.getPlot();
        setRecommendedColorsForPieChart(plot, sortedKeys.size());

        // 设置饼图透明度，使颜色更柔和
        plot.setForegroundAlpha(0.85f);

        return chart;
    }

    /**
     * 判断是否为月份数据（如"2025-06"格式）
     */
    private static boolean isMonthData(java.util.List<String> keys) {
        if (keys.isEmpty()) {
            return false;
        }

        // 检查是否大部分键符合年月格式
        int matchCount = 0;
        for (String key : keys) {
            // 匹配 "2025-06" 或 "2025-6" 格式
            if (key.matches("\\d{4}-\\d{1,2}")) {
                matchCount++;
            }
        }

        boolean isMonth = matchCount > keys.size() / 2;
        log.debug("月份数据判断: keys={}, matchCount={}, threshold={}, result={}", 
                keys, matchCount, keys.size() / 2, isMonth);
        
        // 如果超过一半的键符合格式，则认为是月份数据
        return isMonth;
    }

    /**
     * 对月份格式的键进行排序
     */
    private static void sortMonthKeys(java.util.List<String> keys) {
        log.debug("开始对月份键进行排序，原始顺序: {}", keys);
        
        keys.sort((key1, key2) -> {
            try {
                // 直接使用字符串比较，因为年月格式 "YYYY-MM" 本身就是可排序的
                // 但需要确保月份是两位数格式
                String normalizedKey1 = normalizeMonthKey(key1);
                String normalizedKey2 = normalizeMonthKey(key2);
                
                int result = normalizedKey1.compareTo(normalizedKey2);
                log.debug("比较标准化后的键: {} vs {}, 结果: {}", normalizedKey1, normalizedKey2, result);
                return result;
                
            } catch (Exception e) {
                // 解析失败时记录错误
                log.error("月份排序错误: {}, key1={}, key2={}", e.getMessage(), key1, key2);
                // 如果解析失败，使用原始字符串比较
                return key1.compareTo(key2);
            }
        });
        
        log.debug("月份键排序完成，最终顺序: {}", keys);
    }
    
    /**
     * 标准化月份键，确保月份是两位数格式
     */
    private static String normalizeMonthKey(String key) {
        String[] parts = key.split("-");
        if (parts.length >= 2) {
            String year = parts[0];
            String month = parts[1];
            
            // 确保月份是两位数
            if (month.length() == 1) {
                month = "0" + month;
            }
            
            return year + "-" + month;
        }
        return key; // 如果格式不正确，返回原始键
    }

    /**
     * 检查是否包含星期数据
     */
    private static boolean containsWeekdayData(java.util.List<String> keys) {
        if (keys.isEmpty()) {
            return false;
        }

        // 定义中文星期几的常见表示
        java.util.Set<String> weekdayTerms = new java.util.HashSet<>(java.util.Arrays.asList(
                "周一", "周二", "周三", "周四", "周五", "周六", "周日",
                "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日",
                "星期天", "周天", "礼拜一", "礼拜二", "礼拜三", "礼拜四", "礼拜五", "礼拜六", "礼拜日", "礼拜天"
        ));

        // 检查是否有星期相关的键
        for (String key : keys) {
            if (weekdayTerms.contains(key)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 对星期数据进行排序
     */
    private static void sortWeekdayKeys(java.util.List<String> keys) {
        // 定义星期的顺序映射
        java.util.Map<String, Integer> weekdayOrder = new java.util.HashMap<>();

        // 周一到周日的顺序
        weekdayOrder.put("周一", 1);
        weekdayOrder.put("周二", 2);
        weekdayOrder.put("周三", 3);
        weekdayOrder.put("周四", 4);
        weekdayOrder.put("周五", 5);
        weekdayOrder.put("周六", 6);
        weekdayOrder.put("周日", 7);
        weekdayOrder.put("周天", 7);

        // 星期一到星期日的顺序
        weekdayOrder.put("星期一", 1);
        weekdayOrder.put("星期二", 2);
        weekdayOrder.put("星期三", 3);
        weekdayOrder.put("星期四", 4);
        weekdayOrder.put("星期五", 5);
        weekdayOrder.put("星期六", 6);
        weekdayOrder.put("星期日", 7);
        weekdayOrder.put("星期天", 7);

        // 礼拜一到礼拜日的顺序
        weekdayOrder.put("礼拜一", 1);
        weekdayOrder.put("礼拜二", 2);
        weekdayOrder.put("礼拜三", 3);
        weekdayOrder.put("礼拜四", 4);
        weekdayOrder.put("礼拜五", 5);
        weekdayOrder.put("礼拜六", 6);
        weekdayOrder.put("礼拜日", 7);
        weekdayOrder.put("礼拜天", 7);

        // 根据星期顺序排序
        keys.sort((key1, key2) -> {
            Integer order1 = weekdayOrder.getOrDefault(key1, Integer.MAX_VALUE);
            Integer order2 = weekdayOrder.getOrDefault(key2, Integer.MAX_VALUE);

            return Integer.compare(order1, order2);
        });
    }

    /**
     * 检查是否包含工单状态数据
     */
    private static boolean containsWorkOrderStatusData(java.util.List<String> keys) {
        if (keys.isEmpty()) {
            return false;
        }

        // 定义工单状态的常见表示
        java.util.Set<String> workOrderStatusTerms = new java.util.HashSet<>(java.util.Arrays.asList(
                "已创建", "待审批", "已派工", "处理中", "已完成", "已终止", "已存档"
        ));

        // 检查是否有工单状态相关的键
        int matchCount = 0;
        for (String key : keys) {
            if (workOrderStatusTerms.contains(key)) {
                matchCount++;
            }
        }

        // 如果超过一半的键是工单状态，则认为是工单状态数据
        return matchCount > keys.size() / 2;
    }

    /**
     * 对工单状态数据进行排序
     * 按照业务流程顺序排序：已创建 -> 待审批 -> 已派工 -> 处理中 -> 已完成/已终止 -> 已存档
     */
    private static void sortWorkOrderStatusKeys(java.util.List<String> keys) {
        // 定义工单状态的顺序映射
        java.util.Map<String, Integer> workOrderStatusOrder = new java.util.HashMap<>();

        // 设置工单状态的业务流程顺序
        workOrderStatusOrder.put("已创建", 1);
        workOrderStatusOrder.put("待审批", 2);
        workOrderStatusOrder.put("已派工", 3);
        workOrderStatusOrder.put("处理中", 4);
        workOrderStatusOrder.put("已完成", 5);
        workOrderStatusOrder.put("已终止", 6);
        workOrderStatusOrder.put("已存档", 7);

        // 根据工单状态顺序排序
        keys.sort((key1, key2) -> {
            Integer order1 = workOrderStatusOrder.getOrDefault(key1, Integer.MAX_VALUE);
            Integer order2 = workOrderStatusOrder.getOrDefault(key2, Integer.MAX_VALUE);

            return Integer.compare(order1, order2);
        });
    }

    /**
     * 创建折线图
     */
    private static JFreeChart createLineChart(String title, String xAxisLabel, String yAxisLabel, Map<Number, Number> data) {
        XYSeries series = new XYSeries("数据");

        // 对键进行排序
        java.util.List<Number> sortedKeys = new java.util.ArrayList<>(data.keySet());
        sortedKeys.sort(Comparator.comparingDouble(Number::doubleValue));

        for (Number key : sortedKeys) {
            series.add(key, data.get(key));
        }

        XYSeriesCollection dataset = new XYSeriesCollection();
        dataset.addSeries(series);

        JFreeChart chart = ChartFactory.createXYLineChart(
                title,
                xAxisLabel,
                yAxisLabel,
                dataset,
                PlotOrientation.VERTICAL,
                true,
                true,
                false
        );

        // 设置随机美观颜色
        org.jfree.chart.plot.XYPlot plot = chart.getXYPlot();
        setRandomRecommendedColorForXYPlot(plot);

        return chart;
    }

    /**
     * 创建散点图
     */
    private static JFreeChart createScatterChart(String title, String xAxisLabel, String yAxisLabel, Map<Number, Number> data) {
        XYSeries series = new XYSeries("数据");
        for (Map.Entry<Number, Number> entry : data.entrySet()) {
            series.add(entry.getKey(), entry.getValue());
        }

        XYSeriesCollection dataset = new XYSeriesCollection();
        dataset.addSeries(series);

        JFreeChart chart = ChartFactory.createScatterPlot(
                title,
                xAxisLabel,
                yAxisLabel,
                dataset,
                PlotOrientation.VERTICAL,
                true,
                true,
                false
        );

        // 设置随机美观颜色
        org.jfree.chart.plot.XYPlot plot = chart.getXYPlot();
        setRandomRecommendedColorForXYPlot(plot);

        return chart;
    }

    /**
     * 为柱状图设置随机推荐颜色
     */
    private static void setRandomRecommendedColor(org.jfree.chart.plot.CategoryPlot plot) {
        // 随机选择一个推荐颜色
        Color selectedColor = getRandomRecommendedColor();

        // 设置柱状图渲染器颜色
        org.jfree.chart.renderer.category.BarRenderer renderer =
                (org.jfree.chart.renderer.category.BarRenderer) plot.getRenderer();

        // 创建简单的渐变色
        Color lighterColor = new Color(
                Math.min(255, selectedColor.getRed() + 60),
                Math.min(255, selectedColor.getGreen() + 60),
                Math.min(255, selectedColor.getBlue() + 60)
        );

        // 直接使用GradientPaint，不需要额外的transformer
        java.awt.GradientPaint gradientPaint = new java.awt.GradientPaint(
                0.0f, 0.0f, selectedColor,
                0.0f, 1000.0f, lighterColor,
                false);

        renderer.setSeriesPaint(0, gradientPaint);

        // 设置柱子的边框
        renderer.setDrawBarOutline(true);
        renderer.setSeriesOutlinePaint(0, selectedColor.darker());
        renderer.setSeriesOutlineStroke(0, new java.awt.BasicStroke(1.0f));
    }

    /**
     * 为XY图表（折线图、散点图）设置随机推荐颜色
     */
    private static void setRandomRecommendedColorForXYPlot(org.jfree.chart.plot.XYPlot plot) {
        // 随机选择一个推荐颜色
        Color selectedColor = getRandomRecommendedColor();

        // 设置线条或点的颜色
        plot.getRenderer().setSeriesPaint(0, selectedColor);

        // 对于折线图，设置线条粗细
        if (plot.getRenderer() instanceof org.jfree.chart.renderer.xy.XYLineAndShapeRenderer renderer) {
            renderer.setSeriesStroke(0, new java.awt.BasicStroke(2.0f));
        }
    }

    /**
     * 为饼图设置推荐颜色方案
     */
    private static void setRecommendedColorsForPieChart(org.jfree.chart.plot.PiePlot plot, int itemCount) {
        // 确保有足够的颜色
        int colorCount = Math.min(itemCount, RECOMMENDED_COLORS.length);

        // 随机打乱颜色数组，以获得不同的颜色组合
        java.util.List<Color> shuffledColors = new java.util.ArrayList<>();
        Collections.addAll(shuffledColors, RECOMMENDED_COLORS);
        java.util.Collections.shuffle(shuffledColors);

        // 为每个扇区设置颜色
        for (int i = 0; i < itemCount; i++) {
            Color baseColor = shuffledColors.get(i % shuffledColors.size());

            // 为饼图设置略微透明的颜色，使其看起来更柔和
            plot.setSectionPaint(i, baseColor);

            // 设置每个扇区的轮廓
            plot.setSectionOutlineStroke(i, new java.awt.BasicStroke(0.5f));
            plot.setSectionOutlinePaint(i, new Color(240, 240, 240));
        }

        // 设置饼图显示轮廓
        plot.setSectionOutlinesVisible(true);
    }

    /**
     * 获取随机推荐颜色
     */
    private static Color getRandomRecommendedColor() {
        int randomIndex = (int) (Math.random() * RECOMMENDED_COLORS.length);
        return RECOMMENDED_COLORS[randomIndex];
    }

    /**
     * 将JFreeChart转换为URL
     */
    private String chartToUrl(JFreeChart chart, int width, int height) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ChartUtils.writeChartAsPNG(outputStream, chart, width, height);

            // 上传临时文件
            String path = fileBizService.uploadTempFile(outputStream, "chart.png");

            // 获取当前请求的域名信息
            String domain = getDomain();

            // 构建完整URL
            return domain + "/file/download/temp/" + path;
        } catch (IOException e) {
            throw new RuntimeException("图表生成失败", e);
        }
    }

    /**
     * 获取当前请求的域名信息
     */
    private String getDomain() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String scheme = request.getScheme();
                String serverName = request.getServerName();
                int serverPort = request.getServerPort();

                // 构建基础URL
                StringBuilder url = new StringBuilder();
                url.append(scheme).append("://").append(serverName);

                // 添加端口（如果不是默认端口）
                if ((scheme.equals("http") && serverPort != 80) ||
                        (scheme.equals("https") && serverPort != 443)) {
                    url.append(":").append(serverPort);
                }

                // 添加上下文路径
                if (contextPath != null && !contextPath.isEmpty()) {
                    if (!contextPath.startsWith("/")) {
                        url.append("/");
                    }
                    url.append(contextPath);
                }

                return url.toString();
            }
        } catch (Exception e) {
            // 如果获取失败，使用默认值
        }

        // 默认返回相对路径
        return contextPath != null && !contextPath.isEmpty() ? contextPath : "";
    }

    /**
     * 设置图表中文字体，解决中文乱码问题
     *
     * @param chart 图表对象
     */
    private void setChineseFont(JFreeChart chart) {
        // 使用默认字体大小
        setChineseFont(chart, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 设置图表中文字体，根据图表尺寸自动调整字体大小
     *
     * @param chart  图表对象
     * @param width  图表宽度
     * @param height 图表高度
     */
    private void setChineseFont(JFreeChart chart, int width, int height) {
        try {
            // 根据图表尺寸自动计算合适的字体大小
            int[] fontSizes = calculateFontSizes(width, height);
            int titleFontSize = fontSizes[0];
            int labelFontSize = fontSizes[1];
            int tickLabelFontSize = fontSizes[2]; // 新增刻度标签字体大小

            Font chineseFont = new Font("宋体", Font.PLAIN, labelFontSize);
            Font titleFont = new Font("宋体", Font.BOLD, titleFontSize);
            Font tickLabelFont = new Font("宋体", Font.PLAIN, tickLabelFontSize);

            // 设置标题字体
            if (chart.getTitle() != null) {
                chart.getTitle().setFont(titleFont);
            }

            // 设置图例字体
            if (chart.getLegend() != null) {
                chart.getLegend().setItemFont(chineseFont);
            }

            // 根据图表类型设置坐标轴字体
            if (chart.getPlot() instanceof org.jfree.chart.plot.CategoryPlot plot) {
                plot.getDomainAxis().setLabelFont(chineseFont);
                plot.getDomainAxis().setTickLabelFont(tickLabelFont); // 使用专门的刻度标签字体
                plot.getRangeAxis().setLabelFont(chineseFont);
                plot.getRangeAxis().setTickLabelFont(tickLabelFont);

                // 增加柱状图下方标签的边距
                plot.getDomainAxis().setLowerMargin(0.02);
                plot.getDomainAxis().setUpperMargin(0.02);

                // 设置柱子之间的间距
                org.jfree.chart.renderer.category.BarRenderer renderer =
                        (org.jfree.chart.renderer.category.BarRenderer) plot.getRenderer();
                renderer.setItemMargin(0.1);
            } else if (chart.getPlot() instanceof org.jfree.chart.plot.XYPlot plot) {
                plot.getDomainAxis().setLabelFont(chineseFont);
                plot.getDomainAxis().setTickLabelFont(tickLabelFont);
                plot.getRangeAxis().setLabelFont(chineseFont);
                plot.getRangeAxis().setTickLabelFont(tickLabelFont);
            }
        } catch (Exception e) {
            // 设置字体失败不应该影响整个图表生成
            log.error("设置字体失败", e);
        }
    }

    /**
     * 根据图表尺寸自动计算合适的字体大小
     *
     * @param width  图表宽度
     * @param height 图表高度
     * @return 返回字体大小数组 [标题字体大小, 标签字体大小, 刻度标签字体大小]
     */
    private int[] calculateFontSizes(int width, int height) {
        // 标题字体大小：图表宽度的约1/30，但不小于DEFAULT_TITLE_FONT_SIZE
        int titleFontSize = Math.max(DEFAULT_TITLE_FONT_SIZE, width / 30);

        // 标签字体大小：图表宽度的约1/50，但不小于DEFAULT_LABEL_FONT_SIZE
        int labelFontSize = Math.max(DEFAULT_LABEL_FONT_SIZE, width / 50);

        // 刻度标签字体大小：确保X轴标签可见，特别是当有很多类别时
        // 对于工单按小时分布这种有24个类别的图表，需要更大的字体
        int tickLabelFontSize = Math.max(14, width / 60);

        return new int[]{titleFontSize, labelFontSize, tickLabelFontSize};
    }
}