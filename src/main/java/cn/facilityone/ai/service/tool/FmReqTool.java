package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.config.log.LogParam;
import cn.facilityone.ai.dto.FmCreateReqDTO;
import cn.facilityone.ai.dto.FmSearchReqOrderDTO;
import cn.facilityone.ai.dto.FmSearchWechatReqOrderDTO;
import cn.facilityone.ai.properties.FmProperties;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:17
 */

@Slf4j
@Service
public class FmReqTool {

    @Resource
    private FmProperties fmProperties;

    @LogParam
    public List<Map<String, Object>> getReqType(Long projectId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("projectId", projectId);
        try {
            String result = FmBaseTool.fmHttpUtil("/ai/req/getReqType", JSONUtil.toJsonStr(params), Method.POST);
            if (StrUtil.isBlank(result)) {
                return Collections.emptyList();
            }
            JSONObject jsonObject = JSONUtil.parseObj(result);

            // 验证必要字段是否存在
            if (!jsonObject.containsKey("code") || !jsonObject.containsKey("data")) {
                log.warn("工具getReqType返回缺少必要字段");
                return Collections.emptyList();
            }

            String code = jsonObject.getStr("code");
            if (!"200".equals(code)) {
                return Collections.emptyList();
            }

            JSONArray dataArray = jsonObject.getJSONArray("data");
            List<Map<String, Object>> reqTypeList = new ArrayList<>();
            for (Object obj : dataArray) {
                if (obj instanceof Map) {
                    reqTypeList.add((Map<String, Object>) obj);
                } else {
                    log.warn("工具getReqType返回数据中包含非Map类型元素：{}", obj);
                }
            }
            return reqTypeList;

        } catch (Exception e) {
            log.error("工具getReqType调用过程中发生异常", e);
            return Collections.emptyList();
        }
    }

    @LogParam
    public String searchReqOrders(FmSearchReqOrderDTO fmSearchReqOrderDTO) {
        if (fmSearchReqOrderDTO == null || fmSearchReqOrderDTO.getProjectId() == null || fmSearchReqOrderDTO.getUserId() == null) {
            return "参数错误，缺少当前操作用户的userId和项目ID";
        }
        return FmBaseTool.fmHttpUtil("/ai/req/searchReqOrders", JSONUtil.toJsonStr(fmSearchReqOrderDTO), Method.POST);
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    public String searchWechatReqOrders(FmSearchWechatReqOrderDTO fmSearchWechatReqOrderDTO) {
        if (fmSearchWechatReqOrderDTO == null || fmSearchWechatReqOrderDTO.getProjectId() == null || StrUtil.isBlank(fmSearchWechatReqOrderDTO.getWechatOpenId())) {
            return "参数错误，缺少当前操作用户的微信openId和项目ID";
        }
        return FmBaseTool.fmHttpUtil("/ai/req/searchReqOrders", JSONUtil.toJsonStr(fmSearchWechatReqOrderDTO), Method.POST);
    }

    @LogParam(tag = LogParam.TAG.TOOL)
    public String createReqWithWechatFormat(FmCreateReqDTO fmCreateReqDTO) {
        if (StrUtil.isBlank(fmCreateReqDTO.getName()) && StrUtil.isBlank(fmCreateReqDTO.getPhone())) {
            return "请补充需求人姓名及联系方式";
        } else {
            if (StrUtil.isBlank(fmCreateReqDTO.getName())) {
                return "请补充需求人姓名";
            }
            if (StrUtil.isBlank(fmCreateReqDTO.getPhone())) {
                return "请补充需求人联系方式";
            }
        }
        if (fmCreateReqDTO.getReqTypeId() == null) {
            return "请补充需求类型";
        }

        String model = """
                      <div class="confirm-card-header" style="margin-bottom: 20px; font-size: 16px; color: #333; font-weight: 500;border-bottom: 1px solid #eee;padding-bottom: 10px;">感谢您的反馈！我已记录下您的报修信息，请您仔细核对下方信息是否准确，若有需要修改的地方，随时与我沟通，我会立即为您调整。</div>
                <table class="confirm-card-body" style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">姓名</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">联系电话</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">预约开始时间</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">预约结束时间</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">位置</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">需求类型</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                    <tr>
                       <th style="text-align: left; padding: 8px; width: 30%; color: #666; font-weight: bold;">问题描述</th>
                       <td style="padding: 8px; color: #333;">{}</td>
                    </tr>
                </table>
                
                <div class="confirm-card-footer" style="margin-top: 15px; text-align: right;padding-top: 10px;border-top: 1px solid #eee;">
                    <button style="margin: 0 10px; padding: 8px 20px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; color: #333;" onclick="modify()">修改</button>
                    <button style="margin: 0 10px; padding: 8px 20px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; color: #333;" onclick="submit()">提交</button>
                </div>
                
                <form style="display: none;">
                    <input type="hidden" name="projectId" value="{}">
                    <input type="hidden" name="name" value="{}">
                    <input type="hidden" name="phone" value="{}">
                    <input type="hidden" name="startTime" value="{}">
                    <input type="hidden" name="endTime" value="{}">
                    <input type="hidden" name="location" value="{}">
                    <input type="hidden" name="buildingId" value="{}">
                    <input type="hidden" name="buildingName" value="{}">
                    <input type="hidden" name="floorId" value="{}">
                    <input type="hidden" name="floorName" value="{}">
                    <input type="hidden" name="roomId" value="{}">
                    <input type="hidden" name="roomName" value="{}">
                    <input type="hidden" name="reqTypeId" value="{}">
                    <input type="hidden" name="reqTypeName" value="{}">
                    <input type="hidden" name="problemDesc" value="{}">
                </form>
                """;

        // 参数处理
        String location = "";
        if (fmCreateReqDTO.getBuildingId() != null && fmCreateReqDTO.getBuildingId() > 0) {
            location = fmCreateReqDTO.getBuildingName();
        }
        if (fmCreateReqDTO.getFloorId() != null && fmCreateReqDTO.getFloorId() > 0) {
            location += " / " + fmCreateReqDTO.getFloorName();
        }
        if (fmCreateReqDTO.getRoomId() != null && fmCreateReqDTO.getRoomId() > 0) {
            location += " / " + fmCreateReqDTO.getRoomName();
        }
        if (fmCreateReqDTO.getAppointmentStartTime() == null) {
            fmCreateReqDTO.setAppointmentStartTime("");
        }
        if (fmCreateReqDTO.getAppointmentEndTime() == null) {
            fmCreateReqDTO.setAppointmentEndTime("");
        }
        if (fmCreateReqDTO.getRoomId() == null) {
            fmCreateReqDTO.setRoomName("");
            fmCreateReqDTO.setRoomId(0L);
        }
        if (fmCreateReqDTO.getFloorId() == null) {
            fmCreateReqDTO.setFloorName("");
            fmCreateReqDTO.setFloorId(0L);
        }
        if (fmCreateReqDTO.getBuildingId() == null) {
            fmCreateReqDTO.setBuildingName("");
            fmCreateReqDTO.setBuildingId(0L);
        }
        // 预约开始结束时间默认值
        Date now = new Date();
        if (StrUtil.isBlank(fmCreateReqDTO.getAppointmentStartTime())) {
            fmCreateReqDTO.setAppointmentStartTime(DateUtil.format(DateUtil.offsetHour(now, 1), "yyyy-MM-dd HH:mm"));
        }
        if (StrUtil.isBlank(fmCreateReqDTO.getAppointmentEndTime())) {
            fmCreateReqDTO.setAppointmentEndTime(DateUtil.format(DateUtil.offsetDay(now, 1), "yyyy-MM-dd HH:mm"));
        }

        return StrUtil.format(model,
                fmCreateReqDTO.getName(), fmCreateReqDTO.getPhone(),
                fmCreateReqDTO.getAppointmentStartTime(), fmCreateReqDTO.getAppointmentEndTime(),
                location, fmCreateReqDTO.getReqTypeName(), fmCreateReqDTO.getDescription(),

                fmCreateReqDTO.getProjectId(), fmCreateReqDTO.getName(), fmCreateReqDTO.getPhone(),
                fmCreateReqDTO.getAppointmentStartTime(), fmCreateReqDTO.getAppointmentEndTime(),
                location, fmCreateReqDTO.getBuildingId(), fmCreateReqDTO.getBuildingName(),
                fmCreateReqDTO.getFloorId(), fmCreateReqDTO.getFloorName(),
                fmCreateReqDTO.getRoomId(), fmCreateReqDTO.getRoomName(),
                fmCreateReqDTO.getReqTypeId(), fmCreateReqDTO.getReqTypeName(), fmCreateReqDTO.getDescription());
    }

}

