package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.config.log.LogParam;
import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * 动态连接MySQL/PgSql等数据库并执行查询的工具
 *
 * 普通查询使用示例：
 * {
 * "url": "***********************************",
 * "username": "root",
 * "password": "password",
 * "sql": "SELECT * FROM users WHERE status = 'active'",
 * "limit": 50
 * }
 *
 * 参数化查询使用示例（推荐，避免SQL注入）：
 * {
 * "url": "***********************************",
 * "username": "root",
 * "password": "password",
 * "sql": "SELECT * FROM users WHERE status = ? AND age > ?",
 * "parameters": ["active", 18],
 * "limit": 50
 * }
 */
@Component
@JsonClassDescription("动态连接MySQL数据库并执行查询的工具")
@Slf4j
public class DbTool implements Function<DbTool.Request, DbTool.Response> {

    // SQL注入关键词黑名单
    private static final Set<String> DANGEROUS_KEYWORDS = new HashSet<>(Arrays.asList(
            "DROP", "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE", "TRUNCATE",
            "EXEC", "EXECUTE", "UNION", "SCRIPT", "JAVASCRIPT", "VBSCRIPT",
            "ONLOAD", "ONERROR", "ONCLICK", "EVAL", "EXPRESSION", "APPLET",
            "OBJECT", "EMBED", "FORM", "INPUT", "IFRAME", "META", "LINK"
    ));

    // 危险字符模式 - 优化后的版本
    private static final Pattern[] DANGEROUS_PATTERNS = {
            // 检测明显的注入模式：引号后跟OR/AND条件
            Pattern.compile("'\\s*(OR|or|AND|and)\\s+'", Pattern.CASE_INSENSITIVE),
            // 检测数字比较注入：'1'='1' 或 1=1
            Pattern.compile("(\\d+\\s*=\\s*\\d+|'\\d+'\\s*=\\s*'\\d+')", Pattern.CASE_INSENSITIVE),
            // 检测UNION注入（但排除正常的UNION查询）
            Pattern.compile("'\\s*(UNION|union)\\s+(SELECT|select)", Pattern.CASE_INSENSITIVE),
            // 检测函数执行注入
            Pattern.compile("(\\b(EXEC|exec|EXECUTE|execute)\\b\\s*\\()", Pattern.CASE_INSENSITIVE),
            // 检测SQL注释
            Pattern.compile("(--|#|/\\*|\\*/)", Pattern.CASE_INSENSITIVE),
            // 检测时间延迟函数
            Pattern.compile("(\\b(SLEEP|sleep|BENCHMARK|benchmark|WAITFOR|waitfor)\\b\\s*\\()", Pattern.CASE_INSENSITIVE),
            // 检测明显的注入尝试：引号不闭合后跟关键词
            Pattern.compile("'[^']*$\\s*(OR|or|AND|and|UNION|union)", Pattern.CASE_INSENSITIVE)
    };

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record Request(
            @JsonProperty(required = true)
            @JsonPropertyDescription("数据库连接URL，格式：*******************************")
            @ToolParam(description = "数据库连接URL，格式：*******************************")
            String url,

            @JsonProperty(required = true)
            @JsonPropertyDescription("数据库用户名")
            @ToolParam(description = "数据库用户名")
            String username,

            @JsonProperty(required = true)
            @JsonPropertyDescription("数据库密码")
            @ToolParam(description = "数据库密码")
            String password,

            @JsonProperty(required = true)
            @JsonPropertyDescription("要执行的SQL查询语句")
            @ToolParam(description = "要执行的SQL查询语句")
            String sql,

            @JsonProperty()
            @JsonPropertyDescription("查询结果限制条数，默认100")
            @ToolParam(description = "查询结果限制条数，默认100")
            Integer limit,

            @JsonProperty()
            @JsonPropertyDescription("SQL参数列表，用于参数化查询，避免SQL注入")
            @ToolParam(description = "SQL参数列表，用于参数化查询，避免SQL注入")
            List<Object> parameters
    ) {
    }

    /**
     * 参数化查询请求，推荐使用此方式避免SQL注入
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record ParameterizedRequest(
            @JsonProperty(required = true)
            @JsonPropertyDescription("数据库连接URL，格式：*******************************")
            @ToolParam(description = "数据库连接URL，格式：*******************************")
            String url,

            @JsonProperty(required = true)
            @JsonPropertyDescription("数据库用户名")
            @ToolParam(description = "数据库用户名")
            String username,

            @JsonProperty(required = true)
            @JsonPropertyDescription("数据库密码")
            @ToolParam(description = "数据库密码")
            String password,

            @JsonProperty(required = true)
            @JsonPropertyDescription("要执行的SQL查询语句，使用?作为参数占位符")
            @ToolParam(description = "要执行的SQL查询语句，使用?作为参数占位符")
            String sql,

            @JsonProperty(required = true)
            @JsonPropertyDescription("SQL参数列表，按顺序对应SQL中的?占位符")
            @ToolParam(description = "SQL参数列表，按顺序对应SQL中的?占位符")
            List<Object> parameters,

            @JsonProperty()
            @JsonPropertyDescription("查询结果限制条数，默认100")
            @ToolParam(description = "查询结果限制条数，默认100")
            Integer limit
    ) {
    }

    public record Response(
            @JsonPropertyDescription("查询是否成功")
            boolean success,

            @JsonPropertyDescription("查询结果数据")
            List<Map<String, Object>> data,

            @JsonPropertyDescription("影响的行数")
            int rowCount,

            @JsonPropertyDescription("错误信息")
            String error,

            @JsonPropertyDescription("执行时间(毫秒)")
            long executionTime
    ) {}

    @Override
    @LogParam(tag = LogParam.TAG.METHOD, printReturn = true, excludeParams = {"password"})
    public Response apply(Request request) {
        // 如果请求包含参数，使用参数化查询
        if (request.parameters() != null && !request.parameters().isEmpty()) {
            return executeParameterizedQuery(request.url(), request.username(), request.password(),
                    request.sql(), request.parameters(), request.limit());
        }

        // 否则使用原有的非参数化查询（保持向后兼容）
        return executeQuery(request.url(), request.username(), request.password(),
                request.sql(), request.limit());
    }

    /**
     * 参数化查询执行方法（推荐使用）
     */
    @LogParam(tag = LogParam.TAG.METHOD, printReturn = true)
    public Response executeParameterized(ParameterizedRequest request) {
        return executeParameterizedQuery(request.url(), request.username(), request.password(),
                request.sql(), request.parameters(), request.limit());
    }

    /**
     * 执行参数化查询的核心方法
     */
    private Response executeParameterizedQuery(String url, String username, String password,
                                               String sql, List<Object> parameters, Integer limit) {
        long startTime = System.currentTimeMillis();

        // 参数验证
        if (url == null || username == null || password == null || sql == null) {
            return new Response(false, null, 0, "缺少必要参数", 0);
        }

        if (parameters == null) {
            parameters = new ArrayList<>();
        }

        // 基本SQL安全检查（参数化查询相对安全，但仍需基本检查）
        String basicSqlError = checkBasicSqlSafety(sql);
        if (basicSqlError != null) {
            return new Response(false, null, 0, basicSqlError, 0);
        }

        // SQL安全检查
        String trimmedSql = sql.trim();
        if (!isSelectQuery(trimmedSql)) {
            return new Response(false, null, 0, "仅支持SELECT查询语句", 0);
        }

        // 验证参数数量与占位符数量是否匹配
        long placeholderCount = sql.chars().filter(ch -> ch == '?').count();
        if (placeholderCount != parameters.size()) {
            return new Response(false, null, 0,
                String.format("参数数量不匹配：SQL中有%d个占位符，但提供了%d个参数",
                    placeholderCount, parameters.size()), 0);
        }

        // 添加LIMIT限制
        int queryLimit = limit != null ? limit : 100;
        if (!trimmedSql.toLowerCase().contains("limit")) {
            trimmedSql += " LIMIT " + queryLimit;
        }

        return executeParameterizedQueryInternal(url, username, password, trimmedSql, parameters, startTime);
    }

    /**
     * 原有的非参数化查询方法（保持向后兼容）
     */
    private Response executeQuery(String url, String username, String password, String sql, Integer limit) {
        long startTime = System.currentTimeMillis();

        // 参数验证
        if (url == null || username == null || password == null || sql == null) {
            return new Response(false, null, 0, "缺少必要参数", 0);
        }

        // SQL注入检查
        String sqlInjectionError = checkSqlInjection(sql);
        if (sqlInjectionError != null) {
            return new Response(false, null, 0, sqlInjectionError, 0);
        }

        // SQL安全检查
        String trimmedSql = sql.trim();
        if (!isSelectQuery(trimmedSql)) {
            return new Response(false, null, 0, "仅支持SELECT查询语句", 0);
        }

        // 添加LIMIT限制
        int queryLimit = limit != null ? limit : 100;
        if (!trimmedSql.toLowerCase().contains("limit")) {
            trimmedSql += " LIMIT " + queryLimit;
        }

        return executeParameterizedQueryInternal(url, username, password, trimmedSql, new ArrayList<>(), startTime);
    }

    /**
     * 执行参数化查询的内部实现
     */
    private Response executeParameterizedQueryInternal(String url, String username, String password,
                                                       String sql, List<Object> parameters, long startTime) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            // 创建独立的数据库连接
            connection = DriverManager.getConnection(url, username, password);
            statement = connection.prepareStatement(sql);

            // 设置参数
            for (int i = 0; i < parameters.size(); i++) {
                Object param = parameters.get(i);
                setParameterValue(statement, i + 1, param);
            }

            resultSet = statement.executeQuery();

            // 转换结果集
            List<Map<String, Object>> data = convertResultSet(resultSet);
            long executionTime = System.currentTimeMillis() - startTime;

            return new Response(true, data, data.size(), null, executionTime);

        } catch (SQLException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            return new Response(false, null, 0, "数据库错误: " + e.getMessage(), executionTime);
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            return new Response(false, null, 0, "执行错误: " + e.getMessage(), executionTime);
        } finally {
            // 确保资源释放
            closeResources(resultSet, statement, connection);
        }
    }

    /**
     * 设置PreparedStatement参数值
     */
    private void setParameterValue(PreparedStatement statement, int index, Object value) throws SQLException {
        if (value == null) {
            statement.setNull(index, Types.NULL);
        } else if (value instanceof String) {
            statement.setString(index, (String) value);
        } else if (value instanceof Integer) {
            statement.setInt(index, (Integer) value);
        } else if (value instanceof Long) {
            statement.setLong(index, (Long) value);
        } else if (value instanceof Double) {
            statement.setDouble(index, (Double) value);
        } else if (value instanceof Float) {
            statement.setFloat(index, (Float) value);
        } else if (value instanceof Boolean) {
            statement.setBoolean(index, (Boolean) value);
        } else if (value instanceof java.util.Date) {
            statement.setTimestamp(index, new Timestamp(((java.util.Date) value).getTime()));
        } else {
            // 对于其他类型，转换为字符串
            statement.setString(index, value.toString());
        }
    }

    /**
     * 基本SQL安全检查（用于参数化查询）
     */
    private String checkBasicSqlSafety(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "SQL语句不能为空";
        }

        // 检查是否包含多语句
        String[] statements = sql.split(";");
        if (statements.length > 1) {
            int nonEmptyStatements = 0;
            for (String stmt : statements) {
                if (!stmt.trim().isEmpty()) {
                    nonEmptyStatements++;
                }
            }
            if (nonEmptyStatements > 1) {
                return "不允许执行多条SQL语句";
            }
        }

        // 检查危险的DDL/DML关键词（使用单词边界匹配，避免误报）
        String[] dangerousKeywords = {"DROP", "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE", "TRUNCATE"};
        for (String keyword : dangerousKeywords) {
            // 使用单词边界匹配，确保是完整的关键词而不是字段名的一部分
            Pattern keywordPattern = Pattern.compile("\\b" + keyword + "\\b", Pattern.CASE_INSENSITIVE);
            if (keywordPattern.matcher(sql).find()) {
                return "SQL语句包含危险关键词: " + keyword;
            }
        }

        return null; // 通过检查
    }

    /**
     * SQL注入检查 - 优化版本（用于非参数化查询）
     */
    private String checkSqlInjection(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "SQL语句不能为空";
        }

        // 1. 检查危险关键词（使用单词边界匹配）
        Set<String> filteredKeywords = new HashSet<>(DANGEROUS_KEYWORDS);
        // 移除可能在正常查询中出现的关键词
        filteredKeywords.remove("UNION"); // UNION可能在正常查询中使用

        for (String keyword : filteredKeywords) {
            // 使用单词边界匹配，避免误判字段名
            Pattern keywordPattern = Pattern.compile("\\b" + keyword + "\\b", Pattern.CASE_INSENSITIVE);
            if (keywordPattern.matcher(sql).find()) {
                return "SQL语句包含危险关键词: " + keyword;
            }
        }

        // 2. 检查危险模式
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            if (pattern.matcher(sql).find()) {
                return "SQL语句包含可疑的注入模式";
            }
        }

        // 3. 检查多语句执行（但允许存储过程调用中的分号）
        String[] statements = sql.split(";");
        if (statements.length > 1) {
            // 检查是否有多个非空语句
            int nonEmptyStatements = 0;
            for (String stmt : statements) {
                if (!stmt.trim().isEmpty()) {
                    nonEmptyStatements++;
                }
            }
            if (nonEmptyStatements > 1) {
                return "不允许执行多条SQL语句";
            }
        }

        // 4. 检查引号平衡
        if (!isQuotesBalanced(sql)) {
            return "SQL语句引号不平衡，可能存在注入风险";
        }

        // 5. 检查括号平衡
        if (!isParenthesesBalanced(sql)) {
            return "SQL语句括号不平衡";
        }

        return null; // 通过检查
    }

    /**
     * 检查引号是否平衡
     */
    private boolean isQuotesBalanced(String sql) {
        int singleQuotes = 0;
        int doubleQuotes = 0;
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;

        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);

            if (c == '\'' && !inDoubleQuote) {
                if (i == 0 || sql.charAt(i - 1) != '\\') {
                    inSingleQuote = !inSingleQuote;
                    singleQuotes++;
                }
            } else if (c == '"' && !inSingleQuote) {
                if (i == 0 || sql.charAt(i - 1) != '\\') {
                    inDoubleQuote = !inDoubleQuote;
                    doubleQuotes++;
                }
            }
        }

        return singleQuotes % 2 == 0 && doubleQuotes % 2 == 0;
    }

    /**
     * 检查括号是否平衡
     */
    private boolean isParenthesesBalanced(String sql) {
        int count = 0;
        for (char c : sql.toCharArray()) {
            if (c == '(') count++;
            else if (c == ')') count--;
            if (count < 0) return false;
        }
        return count == 0;
    }

    private boolean isSelectQuery(String sql) {
        String upperSql = sql.toUpperCase().trim();
        return upperSql.startsWith("SELECT") || upperSql.startsWith("WITH");
    }

    private List<Map<String, Object>> convertResultSet(ResultSet rs) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        while (rs.next()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = rs.getObject(i);
                row.put(columnName, value);
            }
            result.add(row);
        }

        return result;
    }

    private void closeResources(ResultSet rs, Statement stmt, Connection conn) {
        try {
            if (rs != null) rs.close();
        } catch (SQLException ignored) {
        }

        try {
            if (stmt != null) stmt.close();
        } catch (SQLException ignored) {
        }

        try {
            if (conn != null) conn.close();
        } catch (SQLException ignored) {
        }
    }

    /**
     * 检查表是否存在的具体实现
     */
    public boolean checkTableExists(String url, String username, String password, String tableName) {
        if (url == null || username == null || password == null || tableName == null) {
            return false;
        }

        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;

        try {
            connection = DriverManager.getConnection(url, username, password);

            // 使用INFORMATION_SCHEMA查询表是否存在
            String sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ? AND TABLE_SCHEMA = DATABASE()";
            statement = connection.prepareStatement(sql);
            statement.setString(1, tableName);
            resultSet = statement.executeQuery();

            if (resultSet.next()) {
                return resultSet.getInt(1) > 0;
            }

        } catch (SQLException e) {
            log.error("检查表是否存在时发生错误: {}", e.getMessage());
        } finally {
            closeResources(resultSet, statement, connection);
        }

        return false;
    }
}
