package cn.facilityone.ai.service.flow;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.entity.*;
import cn.facilityone.ai.service.db.UserConversationService;
import cn.facilityone.ai.util.PromptsUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@Slf4j
public class FlowService {

    private final UserConversationService userConversationService;
    private final FmFlowService fmFlowService;
    private final ChatClient simpleClient;

    @Autowired
    public FlowService(FmFlowService fmFlowService, UserConversationService userConversationService, @Qualifier("simpleClient") ChatClient simpleClient) {
        this.fmFlowService = fmFlowService;
        this.userConversationService = userConversationService;
        this.simpleClient = simpleClient;
    }


    /**
     * 处理流式请求
     */
    public Flux<AiChatResponse> handleFlowStream(AiChatRequest aiChatRequest) {
        Flux<AiChatResponse> flux = handleFlow(aiChatRequest, true);
        // 添加结束标记
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();
        AiChatResponse endMarker = AiChatResponse
                .createResponse("", contentInfo.getConversationId(),
                        new AiChatResponseMeta(AiChatResponseMeta.ExtraAction.END));
        return flux.concatWithValues(endMarker);
    }

    /**
     * 处理阻塞请求
     */
    public AiChatResponse handleFlowBlocking(AiChatRequest aiChatRequest) {
        AiChatResponse aiChatResponse = handleFlow(aiChatRequest, false).blockFirst();
        // 处理对话结束标识
        AiChatResponseMeta metadata;
        if (aiChatResponse != null) {
            metadata = aiChatResponse.getMetadata();
            if (metadata == null) {
                metadata = new AiChatResponseMeta();
            }
            metadata.setExtraAction(AiChatResponseMeta.ExtraAction.END);
            aiChatResponse.setMetadata(metadata);
        }
        return aiChatResponse;
    }

    /**
     * 内部通用逻辑处理
     */
    public Flux<AiChatResponse> handleFlow(AiChatRequest aiChatRequest, boolean isStream) {
        // 获取对话用户信息
        ApiAuthenticationToken authentication =
                (ApiAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        ContentInfo contentInfo = authentication.getContentInfo();

        // 处理对话ID
        String conversationId = aiChatRequest.getConversationId();
        if (StrUtil.isBlank(conversationId)) {
            // 初始化对话ID
            conversationId = IdUtil.getSnowflakeNextIdStr();
            // 保存用户对话关联信息
            // 生成对话标题
            String title = simpleClient.prompt()
                    .user(u -> u.text(PromptsUtil.getPrompts("common/generate-title"))
                            .param("msg", aiChatRequest.getMessage()))
                    .call().content();
            UserConversationEntity userConversationEntity = new UserConversationEntity(contentInfo.getUserId(), conversationId, title, contentInfo.getTenantId());
            userConversationService.save(userConversationEntity);
        }
        contentInfo.setConversationId(conversationId);

        // 根据appCode路由到正确的后续处理逻辑
        String appCode = contentInfo.getAppCode();
        if (appCode.startsWith("fm-")) {
            return fmFlowService.handleFmFlow(aiChatRequest.getMessage(), aiChatRequest.getProjectId(), isStream);
        } else {
            return Flux.just(AiChatResponse.createResponse("该工作流暂不存在，请稍后再试", conversationId));
        }
    }
}
