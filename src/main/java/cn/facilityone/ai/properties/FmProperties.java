/*
 * Copyright 2006-2019 FacilityONE Inc. All Rights Reserved
 *
 * 注意：
 * 本软件内容仅限于费哲软件内部传阅，禁止外泄以及用于其他商业目的
 * 费哲软件(FacilityONE) : www.facilityone.cn
 */

package cn.facilityone.ai.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Data
@Component
@ConfigurationProperties(prefix = "fm")
public class FmProperties {

    /**
     * 基础配置
     */
    private Base base = new Base();

    @Data
    public static class Base {
        /**
         * 多租户配置
         */
        private MultiTenant multiTenant = new MultiTenant();
    }

    @Data
    public static class MultiTenant extends HashMap<String, TenantConfig> {

        /**
         * 获取默认租户配置
         */
        public TenantConfig getDefaultConfig() {
            return this.get("default");
        }

        /**
         * 获取指定租户配置
         */
        public TenantConfig getTenantConfig(String tenantId) {
            return this.get(tenantId);
        }
    }

    @Data
    public static class TenantConfig {

        public enum Type {
            FM2_0,SHANG,SPACE
        }

        public enum ToolType {
            API,DB
        }

        /**
         * 基础地址
         */
        private String baseUrl;

        /**
         * token
         */
        private String bearerToken;

        /**
         * 类型:FM2_0;SHANG;SPACE
         */
        private String type;

        /**
         * 工具类型:API;DB
         */
        private String toolType;

        /**
         * 数据库连接地址
         */
        private String dbUrl;

        /**
         * 数据库用户名
         */
        private String dbUserName;

        /**
         * 数据库密码
         */
        private String dbPassword;
    }
}