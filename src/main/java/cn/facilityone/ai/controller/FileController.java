package cn.facilityone.ai.controller;

import cn.facilityone.ai.dto.FileDTO;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.service.biz.FileBizService;
import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/22 18:11
 */


@RestController
@RequestMapping("file")
@Slf4j
public class FileController {

    private final FileBizService fileBizService;

    public FileController(FileBizService fileBizService) {
        this.fileBizService = fileBizService;
    }

    @PostMapping(value = "/upload")
    public RestResult<FileDTO> uploadFile(@RequestParam("file") MultipartFile file) {
        FileEntity fileEntity = fileBizService.uploadFile(file);
        FileDTO fileDTO = BeanUtil.toBean(fileEntity, FileDTO.class);
        return new RestResult<>(RestResult.Status.SUCCESS, fileDTO);
    }

    /**
     * 微信批量上传文件入参
     *
     * @param customerKey
     * @param mediaIds
     */
    public record WechatFileDTO(String customerKey, List<String> mediaIds) {
    }

    /**
     * 微信上传文件
     *
     * @param wechatFileDTO 微信上传文件入参
     * @return 文件列表
     */
    @PostMapping(value = "/uploadWeChat")
    public RestResult<List<FileDTO>> uploadWeChatFile(@RequestBody WechatFileDTO wechatFileDTO) {
        try {
            List<FileEntity> fileEntityList = fileBizService.uploadWeChatFile(wechatFileDTO.mediaIds, wechatFileDTO.customerKey);
            List<FileDTO> fileDTOList = new ArrayList<>(fileEntityList.size());
            for (FileEntity fileEntity : fileEntityList) {
                FileDTO fileDTO = BeanUtil.toBean(fileEntity, FileDTO.class);
                fileDTOList.add(fileDTO);
            }
            return new RestResult<>(RestResult.Status.SUCCESS, fileDTOList);
        } catch (Exception e) {
            return new RestResult<>(RestResult.Status.FAIL, e.getMessage(), null);
        }
    }

    /**
     * 下载临时文件
     *
     * @return 文件资源
     */
    @GetMapping("/download/temp/{year}/{month}/{day}/{name}")
    public ResponseEntity<Resource> downloadTempFile(@PathVariable String name, @PathVariable String year, @PathVariable String month, @PathVariable String day) {
        try {
            String filePath = year + "/" + month + "/" + day + "/" + name;
            // 获取文件
            File file = fileBizService.getTempFile(filePath);

            // 创建文件资源
            Resource resource = new FileSystemResource(file);

            // 从文件路径中提取文件名
            String fileName = file.getName();

            // 设置文件名编码，解决中文文件名问题
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20");

            // 设置响应头
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                    .body(resource);
        } catch (Exception e) {
            log.error("下载临时文件失败: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }
}