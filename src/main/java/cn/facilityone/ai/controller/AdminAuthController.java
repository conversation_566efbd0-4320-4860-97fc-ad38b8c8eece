package cn.facilityone.ai.controller;

import cn.facilityone.ai.config.security.admin.JwtUtils;
import cn.facilityone.ai.config.security.admin.SkipAuth;
import cn.facilityone.ai.dto.AdminUserDTO;
import cn.facilityone.ai.entity.AdminUserEntity;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.service.db.AdminUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/auth")
public class AdminAuthController {

    private final AuthenticationManager authenticationManager;
    private final JwtUtils jwtUtils;

    public AdminAuthController(@Qualifier("adminAuthenticationManager") AuthenticationManager authenticationManager,
                               JwtUtils jwtUtils) {
        this.authenticationManager = authenticationManager;
        this.jwtUtils = jwtUtils;
    }

    @Autowired
    private AdminUserService adminUserService;

    public record LoginRequest(String username, String password) {
    }

    @SkipAuth
    @PostMapping("/login")
    public RestResult<String> login(@RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.username(),
                        loginRequest.password()
                )
        );

        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String jwt = jwtUtils.generateToken(userDetails);

        return RestResult.success(jwt);
    }

    @GetMapping("/info")
    public RestResult<AdminUserDTO> getLoginUserInfo() {
        UsernamePasswordAuthenticationToken authentication = (UsernamePasswordAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        AdminUserEntity userEntity = adminUserService.getOne(new LambdaQueryWrapper<AdminUserEntity>().eq(AdminUserEntity::getUsername, userDetails.getUsername()));
        AdminUserDTO userDTO = AdminUserDTO.of(userEntity);
        return RestResult.success(userDTO);
    }

}