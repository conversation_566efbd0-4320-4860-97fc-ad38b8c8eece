package cn.facilityone.ai.controller;

import cn.facilityone.ai.service.tool.WeatherToolService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 11:25
 */


@RestController
public class OpenAIController {

    private final WeatherToolService weatherToolService;
    private final ChatClient chatClient;

    public OpenAIController(ChatClient chatClient, WeatherToolService weatherToolService) {
        this.chatClient = chatClient;
        this.weatherToolService = weatherToolService;
    }


    // 原始同步接口：一次性返回完整回复
    @GetMapping("/ai")
    public String ollama(@RequestParam String msg) {
        String content = chatClient.prompt().user(msg).tools(weatherToolService).call().content();
        System.out.println(content);
        return content;
    }

    @GetMapping(value = "/ai/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamResponse(@RequestParam String msg) {
        Flux<String> content = chatClient.prompt().user(msg).tools(weatherToolService).stream().content();
        System.out.println(content);
        return content
                .map(response -> new String(response.getBytes(), StandardCharsets.UTF_8));
    }

    /**
     * 格式化日期字符串为"dd/MM/yy"格式
     *
     * @param dateStr 原始日期字符串
     * @return 格式化后的日期字符串
     */
    private String formatDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return "";
        }

        try {
            // 尝试识别常见的日期格式
            String[] formats = {
                    "yyyy-MM-dd", "yyyy/MM/dd", "dd-MM-yyyy", "dd/MM/yyyy",
                    "MM-dd-yyyy", "MM/dd/yyyy", "yyyyMMdd", "yyyy.MM.dd",
                    "dd.MM.yyyy", "yyyy年MM月dd日"
            };

            Date date = null;
            for (String format : formats) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    sdf.setLenient(false);
                    date = sdf.parse(dateStr);
                    if (date != null) {
                        break;
                    }
                } catch (ParseException e) {
                    // 尝试下一种格式
                }
            }

            if (date != null) {
                SimpleDateFormat targetFormat = new SimpleDateFormat("dd/MM/yy");
                return targetFormat.format(date);
            }

            // 如果无法识别，返回原始字符串
            return dateStr;
        } catch (Exception e) {
            System.err.println("日期格式化失败: " + dateStr + ", " + e.getMessage());
            return dateStr;
        }
    }

    /**
     * 格式化时间字符串为"HH:mm:ss"格式
     *
     * @param timeStr 原始时间字符串
     * @return 格式化后的时间字符串
     */
    private String formatTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return "";
        }

        try {
            // 尝试识别常见的时间格式
            String[] formats = {
                    "HH:mm:ss", "HH:mm", "HHmmss", "HHmm",
                    "hh:mm:ss a", "hh:mm a", "HH时mm分ss秒", "HH时mm分"
            };

            Date time = null;
            for (String format : formats) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    sdf.setLenient(false);
                    time = sdf.parse(timeStr);
                    if (time != null) {
                        break;
                    }
                } catch (ParseException e) {
                    // 尝试下一种格式
                }
            }

            if (time != null) {
                SimpleDateFormat targetFormat = new SimpleDateFormat("HH:mm:ss");
                return targetFormat.format(time);
            }

            // 如果无法识别，但包含冒号，可能已经是合适的格式
            if (timeStr.contains(":")) {
                // 确保有秒数
                if (timeStr.split(":").length == 2) {
                    return timeStr + ":00";
                }
                return timeStr;
            }

            // 如果无法识别，返回原始字符串
            return timeStr;
        } catch (Exception e) {
            System.err.println("时间格式化失败: " + timeStr + ", " + e.getMessage());
            return timeStr;
        }
    }

}