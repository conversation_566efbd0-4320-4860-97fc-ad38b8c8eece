package cn.facilityone.ai.controller;

import cn.facilityone.ai.dto.MessageDTO;
import cn.facilityone.ai.dto.UserConversationDTO;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.service.biz.ConversationBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("conversation")
public class ConversationController {

    @Autowired
    private ConversationBizService conversationBizService;

    /**
     * 获取用户的对话列表
     */
    @GetMapping("/list")
    public RestResult<List<UserConversationDTO>> getConversationList() {
        return new RestResult<>(RestResult.Status.SUCCESS, conversationBizService.getConversationList());
    }

    /**
     * 获取指定对话信息
     */
    @GetMapping("/{conversationId}")
    public RestResult<UserConversationDTO> getInfo(@PathVariable("conversationId") String conversationId) {
        return new RestResult<>(RestResult.Status.SUCCESS, conversationBizService.getConversationInfo(conversationId));
    }

    /**
     * 获取会话的历史消息
     */
    @GetMapping("/{conversationId}/messages")
    public RestResult<List<MessageDTO>> getHistoryMessages(@PathVariable("conversationId") String conversationId) {
        return new RestResult<>(RestResult.Status.SUCCESS, conversationBizService.getHistoryMessages(conversationId));
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/{conversationId}")
    public RestResult<String> deleteConversation(@PathVariable String conversationId) {
        boolean r = conversationBizService.deleteConversation(conversationId);
        if (r) {
            return new RestResult<>(RestResult.Status.SUCCESS);
        } else {
            return new RestResult<>(RestResult.Status.PARAM_ERROR, "对话不存在或已删除", null);
        }
    }
}
