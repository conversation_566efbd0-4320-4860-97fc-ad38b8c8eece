package cn.facilityone.ai.controller;

import cn.facilityone.ai.dto.ReportAnalysisResult;
import cn.facilityone.ai.dto.UploadedImage2TextDTO;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.service.biz.FileBizService;
import cn.facilityone.ai.service.biz.ReportBizService;
import cn.facilityone.ai.service.biz.ToolBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 工具控制器
 * 提供各种工具类API接口，包括音频转文字、图片转文字、报表分析等功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:16
 */
@Slf4j
@RestController
@RequestMapping("tool")
public class ToolController {

    private final ToolBizService toolBizService;
    private final FileBizService fileBizService;
    private final ReportBizService reportBizService;

    public ToolController(ToolBizService toolBizService, FileBizService fileBizService, ReportBizService reportBizService) {
        this.toolBizService = toolBizService;
        this.fileBizService = fileBizService;
        this.reportBizService = reportBizService;
    }

    @PostMapping(value = "/audio2Text")
    public RestResult<String> audio2Text(@RequestParam("file") MultipartFile file) {
        try {
            FileEntity fileEntity = fileBizService.uploadFile(file);
            String text = toolBizService.audio2Text(fileEntity);
            return new RestResult<>(RestResult.Status.SUCCESS, text);
        } catch (Exception e) {
            log.error("音频转文字失败 - 文件名: {}, 错误: {}", file.getOriginalFilename(), e.getMessage());
            return new RestResult<>(RestResult.Status.FAIL, "音频转文字失败: " + e.getMessage(), null);
        }
    }

    /**
     * 图片转文字（支持多图）
     *
     * @param files 多图片文件
     * @param type  操作类型：repair-create:工单需求单创建;repair-complete:工单需求单完成。
     * @return 文本内容
     */
    @PostMapping(value = "/image2Text")
    public RestResult<String> multipleImages2Text(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
        try {
            List<FileEntity> fileEntityList = fileBizService.uploadMultipleFile(files);
            String text = toolBizService.multipleImages2Text(fileEntityList, type);
            return new RestResult<>(RestResult.Status.SUCCESS, text);
        } catch (Exception e) {
            log.error("图片转文字失败 - 文件数量: {}, 类型: {}, 错误: {}", files.length, type, e.getMessage());
            return new RestResult<>(RestResult.Status.FAIL, "图片转文字失败: " + e.getMessage(), null);
        }
    }

    /**
     * 图片转文字（支持多图）
     *
     * @return 文本内容
     */
    @PostMapping(value = "/uploadedImage2Text")
    public RestResult<String> uploadedImage2Text(@RequestBody UploadedImage2TextDTO paramDTO) {
        try {
            String text = toolBizService.uploadedImage2Text(paramDTO.getFileIds(), paramDTO.getType());
            return new RestResult<>(RestResult.Status.SUCCESS, text);
        } catch (Exception e) {
            log.error("已上传图片转文字失败 - 文件IDs: {}, 类型: {}, 错误: {}", 
                    paramDTO.getFileIds(), paramDTO.getType(), e.getMessage());
            return new RestResult<>(RestResult.Status.FAIL, "图片转文字失败: " + e.getMessage(), null);
        }
    }

    /**
     * 报表分析接口
     * 根据文件ID对上传的CSV报表文件进行数据分析，包括统计分析和AI智能分析
     * 
     * @param fileId 文件ID，必须是已上传的CSV格式文件
     * @return 包含统计数据和AI分析报告的分析结果
     * 
     * @apiNote 支持的文件格式：CSV
     * @apiNote 分析内容包括：工单状态分布、优先级分布、服务类型分布、时间维度分析、处理时长统计等
     * @apiNote AI分析会根据统计数据生成详细的分析报告和建议
     * 
     * @since 1.0
     */
    @PostMapping(value = "/reportAnalysis")
    public RestResult<ReportAnalysisResult> analyzeReport(@RequestParam Long fileId) {
        log.info("接收到报表分析请求 - 文件ID: {}, 请求时间: {}", fileId, java.time.LocalDateTime.now());
        
        try {
            ReportAnalysisResult result = reportBizService.analyzeReportByFileId(fileId);
            log.info("报表分析成功完成 - 文件ID: {}, 数据量: {}", fileId, result.getDataCount());
            return new RestResult<>(RestResult.Status.SUCCESS, "分析完成", result);
        } catch (Exception e) {
            log.error("报表分析失败 - 文件ID: {}, 错误: {}", fileId, e.getMessage());
            return reportBizService.handleAnalysisException(e);
        }
    }



}
