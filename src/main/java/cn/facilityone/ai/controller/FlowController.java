package cn.facilityone.ai.controller;

import cn.facilityone.ai.entity.AiChatRequest;
import cn.facilityone.ai.entity.AiChatResponse;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.entity.SuggestedInfo;
import cn.facilityone.ai.service.biz.ChatBizService;
import cn.facilityone.ai.service.flow.FlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 18:16
 */

@Slf4j
@RestController
@RequestMapping("flow")
public class FlowController {


    private final FlowService flowService;
    private final ChatBizService chatBizService;

    public FlowController(FlowService flowService, ChatBizService chatBizService) {
        this.flowService = flowService;
        this.chatBizService = chatBizService;
    }

    @PostMapping("/chat")
    public AiChatResponse chat(@RequestBody AiChatRequest aiChatRequest) {
        // 参数校验
        if (aiChatRequest == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        return flowService.handleFlowBlocking(aiChatRequest);
    }


    @PostMapping(value = "/chat-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<AiChatResponse> streamResponse(@RequestBody AiChatRequest aiChatRequest) {
        // 参数校验
        if (aiChatRequest == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        return flowService.handleFlowStream(aiChatRequest);
    }

    /**
     * 获取开场白建议的问题
     */
    @GetMapping(value = "/suggest/{conversationId}")
    public RestResult<SuggestedInfo> suggest(@PathVariable String conversationId) {
        SuggestedInfo suggestedInfo = chatBizService.getSuggestQuestion(conversationId);
        return new RestResult<>(RestResult.Status.SUCCESS, suggestedInfo);
    }

}
