package cn.facilityone.ai.controller;

import cn.facilityone.ai.dto.FileDTO;
import cn.facilityone.ai.entity.FileEntity;
import cn.facilityone.ai.entity.RestResult;
import cn.facilityone.ai.service.biz.FileBizService;
import cn.facilityone.ai.service.biz.VectorBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/9 09:11
 */


@RestController
@RequestMapping("embedding")
public class EmbeddingController {

    private final FileBizService fileBizService;
    private final VectorBizService vectorBizService;

    public EmbeddingController(FileBizService fileBizService, VectorBizService vectorBizService) {
        this.fileBizService = fileBizService;
        this.vectorBizService = vectorBizService;
    }

    @PostMapping(value = "/file")
    public RestResult<FileDTO> embeddingFile(@RequestParam("file") MultipartFile file) {
        FileEntity fileEntity = fileBizService.uploadFile(file);
        vectorBizService.addDocument(fileEntity);
        return new RestResult<>(RestResult.Status.SUCCESS);
    }
}
