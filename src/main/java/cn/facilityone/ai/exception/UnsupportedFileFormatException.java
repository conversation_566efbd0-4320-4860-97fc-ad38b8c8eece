package cn.facilityone.ai.exception;

/**
 * 不支持的文件格式异常
 * 当上传的文件格式不被系统支持时抛出此异常
 */
public class UnsupportedFileFormatException extends RuntimeException {

    /**
     * 文件格式
     */
    private final String fileFormat;

    /**
     * 文件名
     */
    private final String fileName;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public UnsupportedFileFormatException(String message) {
        super(message);
        this.fileFormat = null;
        this.fileName = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public UnsupportedFileFormatException(String message, Throwable cause) {
        super(message, cause);
        this.fileFormat = null;
        this.fileName = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileName 文件名
     * @param fileFormat 文件格式
     */
    public UnsupportedFileFormatException(String message, String fileName, String fileFormat) {
        super(message);
        this.fileName = fileName;
        this.fileFormat = fileFormat;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileName 文件名
     * @param fileFormat 文件格式
     * @param cause 原因异常
     */
    public UnsupportedFileFormatException(String message, String fileName, String fileFormat, Throwable cause) {
        super(message, cause);
        this.fileName = fileName;
        this.fileFormat = fileFormat;
    }

    /**
     * 获取文件格式
     *
     * @return 文件格式
     */
    public String getFileFormat() {
        return fileFormat;
    }

    /**
     * 获取文件名
     *
     * @return 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 获取详细错误信息
     *
     * @return 详细错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder(getMessage());
        if (fileName != null) {
            sb.append(" - 文件名: ").append(fileName);
        }
        if (fileFormat != null) {
            sb.append(" - 文件格式: ").append(fileFormat);
        }
        return sb.toString();
    }

}