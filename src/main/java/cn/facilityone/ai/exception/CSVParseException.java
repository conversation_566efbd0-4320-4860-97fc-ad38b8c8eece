package cn.facilityone.ai.exception;

/**
 * CSV文件解析异常
 * 当CSV文件格式错误或解析失败时抛出此异常
 */
public class CSVParseException extends RuntimeException {

    /**
     * 文件名
     */
    private final String fileName;

    /**
     * 错误行号
     */
    private final Integer errorLineNumber;

    /**
     * 错误列名
     */
    private final String errorColumnName;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public CSVParseException(String message) {
        super(message);
        this.fileName = null;
        this.errorLineNumber = null;
        this.errorColumnName = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public CSVParseException(String message, Throwable cause) {
        super(message, cause);
        this.fileName = null;
        this.errorLineNumber = null;
        this.errorColumnName = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileName 文件名
     */
    public CSVParseException(String message, String fileName) {
        super(message);
        this.fileName = fileName;
        this.errorLineNumber = null;
        this.errorColumnName = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileName 文件名
     * @param errorLineNumber 错误行号
     */
    public CSVParseException(String message, String fileName, Integer errorLineNumber) {
        super(message);
        this.fileName = fileName;
        this.errorLineNumber = errorLineNumber;
        this.errorColumnName = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileName 文件名
     * @param errorLineNumber 错误行号
     * @param errorColumnName 错误列名
     */
    public CSVParseException(String message, String fileName, Integer errorLineNumber, String errorColumnName) {
        super(message);
        this.fileName = fileName;
        this.errorLineNumber = errorLineNumber;
        this.errorColumnName = errorColumnName;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileName 文件名
     * @param errorLineNumber 错误行号
     * @param errorColumnName 错误列名
     * @param cause 原因异常
     */
    public CSVParseException(String message, String fileName, Integer errorLineNumber, String errorColumnName, Throwable cause) {
        super(message, cause);
        this.fileName = fileName;
        this.errorLineNumber = errorLineNumber;
        this.errorColumnName = errorColumnName;
    }

    /**
     * 获取文件名
     *
     * @return 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 获取错误行号
     *
     * @return 错误行号
     */
    public Integer getErrorLineNumber() {
        return errorLineNumber;
    }

    /**
     * 获取错误列名
     *
     * @return 错误列名
     */
    public String getErrorColumnName() {
        return errorColumnName;
    }

    /**
     * 获取详细错误信息
     *
     * @return 详细错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder(getMessage());
        if (fileName != null) {
            sb.append(" - 文件名: ").append(fileName);
        }
        if (errorLineNumber != null) {
            sb.append(" - 错误行号: ").append(errorLineNumber);
        }
        if (errorColumnName != null) {
            sb.append(" - 错误列名: ").append(errorColumnName);
        }
        return sb.toString();
    }
}