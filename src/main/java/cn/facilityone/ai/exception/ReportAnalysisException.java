package cn.facilityone.ai.exception;

/**
 * 报表分析异常
 * 当报表分析过程中发生业务逻辑错误时抛出此异常
 */
public class ReportAnalysisException extends RuntimeException {

    /**
     * 文件ID
     */
    private final Long fileId;

    /**
     * 分析阶段
     */
    private final String analysisPhase;

    /**
     * 错误类型
     */
    private final String errorType;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public ReportAnalysisException(String message) {
        super(message);
        this.fileId = null;
        this.analysisPhase = null;
        this.errorType = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public ReportAnalysisException(String message, Throwable cause) {
        super(message, cause);
        this.fileId = null;
        this.analysisPhase = null;
        this.errorType = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileId 文件ID
     */
    public ReportAnalysisException(String message, Long fileId) {
        super(message);
        this.fileId = fileId;
        this.analysisPhase = null;
        this.errorType = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileId 文件ID
     * @param analysisPhase 分析阶段
     */
    public ReportAnalysisException(String message, Long fileId, String analysisPhase) {
        super(message);
        this.fileId = fileId;
        this.analysisPhase = analysisPhase;
        this.errorType = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileId 文件ID
     * @param analysisPhase 分析阶段
     * @param errorType 错误类型
     */
    public ReportAnalysisException(String message, Long fileId, String analysisPhase, String errorType) {
        super(message);
        this.fileId = fileId;
        this.analysisPhase = analysisPhase;
        this.errorType = errorType;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param fileId 文件ID
     * @param analysisPhase 分析阶段
     * @param errorType 错误类型
     * @param cause 原因异常
     */
    public ReportAnalysisException(String message, Long fileId, String analysisPhase, String errorType, Throwable cause) {
        super(message, cause);
        this.fileId = fileId;
        this.analysisPhase = analysisPhase;
        this.errorType = errorType;
    }

    /**
     * 获取文件ID
     *
     * @return 文件ID
     */
    public Long getFileId() {
        return fileId;
    }

    /**
     * 获取分析阶段
     *
     * @return 分析阶段
     */
    public String getAnalysisPhase() {
        return analysisPhase;
    }

    /**
     * 获取错误类型
     *
     * @return 错误类型
     */
    public String getErrorType() {
        return errorType;
    }

    /**
     * 获取详细错误信息
     *
     * @return 详细错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder(getMessage());
        if (fileId != null) {
            sb.append(" - 文件ID: ").append(fileId);
        }
        if (analysisPhase != null) {
            sb.append(" - 分析阶段: ").append(analysisPhase);
        }
        if (errorType != null) {
            sb.append(" - 错误类型: ").append(errorType);
        }
        return sb.toString();
    }
}