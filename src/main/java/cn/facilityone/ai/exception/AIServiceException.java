package cn.facilityone.ai.exception;

/**
 * AI服务异常
 * 当AI服务调用失败或返回异常时抛出此异常
 */
public class AIServiceException extends RuntimeException {

    /**
     * 服务类型
     */
    private final String serviceType;

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 请求ID（用于追踪）
     */
    private final String requestId;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public AIServiceException(String message) {
        super(message);
        this.serviceType = null;
        this.errorCode = null;
        this.requestId = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause 原因异常
     */
    public AIServiceException(String message, Throwable cause) {
        super(message, cause);
        this.serviceType = null;
        this.errorCode = null;
        this.requestId = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param serviceType 服务类型
     */
    public AIServiceException(String message, String serviceType) {
        super(message);
        this.serviceType = serviceType;
        this.errorCode = null;
        this.requestId = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param serviceType 服务类型
     * @param errorCode 错误代码
     */
    public AIServiceException(String message, String serviceType, String errorCode) {
        super(message);
        this.serviceType = serviceType;
        this.errorCode = errorCode;
        this.requestId = null;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param serviceType 服务类型
     * @param errorCode 错误代码
     * @param requestId 请求ID
     */
    public AIServiceException(String message, String serviceType, String errorCode, String requestId) {
        super(message);
        this.serviceType = serviceType;
        this.errorCode = errorCode;
        this.requestId = requestId;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param serviceType 服务类型
     * @param errorCode 错误代码
     * @param requestId 请求ID
     * @param cause 原因异常
     */
    public AIServiceException(String message, String serviceType, String errorCode, String requestId, Throwable cause) {
        super(message, cause);
        this.serviceType = serviceType;
        this.errorCode = errorCode;
        this.requestId = requestId;
    }

    /**
     * 获取服务类型
     *
     * @return 服务类型
     */
    public String getServiceType() {
        return serviceType;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取请求ID
     *
     * @return 请求ID
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * 获取详细错误信息
     *
     * @return 详细错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder(getMessage());
        if (serviceType != null) {
            sb.append(" - 服务类型: ").append(serviceType);
        }
        if (errorCode != null) {
            sb.append(" - 错误代码: ").append(errorCode);
        }
        if (requestId != null) {
            sb.append(" - 请求ID: ").append(requestId);
        }
        return sb.toString();
    }
}