package cn.facilityone.ai.config.chat;

import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;

import java.util.List;

public class CustomMessageWindowChatMemory implements ChatMemory {
    private final ChatMemoryRepository repository;
    private final int maxMessages;

    public CustomMessageWindowChatMemory(ChatMemoryRepository repository, int maxMessages) {
        this.repository = repository;
        this.maxMessages = maxMessages;
    }

    @Override
    public void add(@NotNull String conversationId, @NotNull List<Message> message) {
        repository.saveAll(conversationId, message);
    }

    @NotNull
    @Override
    public List<Message> get(@NotNull String conversationId) {
        List<Message> messageList = repository.findByConversationId(conversationId);
        return messageList.size() > maxMessages ? messageList.subList(messageList.size() - maxMessages, messageList.size()) : messageList;
    }

    @Override
    public void clear(@NotNull String conversationId) {
        repository.deleteByConversationId(conversationId);
    }
}