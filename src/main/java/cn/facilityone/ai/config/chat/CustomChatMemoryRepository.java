package cn.facilityone.ai.config.chat;

import cn.facilityone.ai.entity.MessageEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

public class CustomChatMemoryRepository implements ChatMemoryRepository {
    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;

    public CustomChatMemoryRepository(DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.objectMapper = new ObjectMapper();
    }

    @NotNull
    @Override
    public List<String> findConversationIds() {
        String sql = "SELECT conversation_id FROM user_conversation";
        return jdbcTemplate.queryForList(sql, String.class);
    }

    @NotNull
    @Override
    public List<Message> findByConversationId(@NotNull String conversationId) {
        String sql = "SELECT message_type, content, metadata FROM messages " +
                "WHERE conversation_id = (SELECT id FROM user_conversation WHERE conversation_id = ?) " +
                "ORDER BY sequence_number ASC";

        return jdbcTemplate.query(sql, messageRowMapper(), conversationId);
    }

    @Override
    @Transactional
    public void saveAll(@NotNull String conversationId, @NotNull List<Message> messages) {
        // 确保会话存在
        Long conversationDbId = ensureConversation(conversationId);

        // 获取当前最大序号
        Integer maxSequence = jdbcTemplate.queryForObject(
                "SELECT COALESCE(MAX(sequence_number), 0) FROM messages WHERE conversation_id = ?",
                Integer.class, conversationDbId);

        if (maxSequence == null) {
            maxSequence = 0;
        }

        // 批量插入消息
        String sql = "INSERT INTO messages (conversation_id, sequence_number, message_type, content, metadata) " +
                "VALUES (?, ?, ?, ?, ?::jsonb)";

        for (int i = 0; i < messages.size(); i++) {
            Message message = messages.get(i);
            jdbcTemplate.update(sql,
                    conversationDbId,
                    maxSequence + i + 1,
                    message.getMessageType().getValue(),
                    message.getText(),
                    serializeMetadata(message)
            );
        }
    }

    @Override
    @Transactional
    public void deleteByConversationId(@NotNull String conversationId) {
        // 先删除消息
        jdbcTemplate.update(
                "DELETE FROM messages WHERE conversation_id = (SELECT id FROM user_conversation WHERE conversation_id = ?)",
                conversationId
        );

        // 再删除会话
        jdbcTemplate.update(
                "DELETE FROM user_conversation WHERE conversation_id = ?",
                conversationId
        );
    }

    private Long ensureConversation(String conversationId) {
        // 查询会话ID是否存在
        Long id = jdbcTemplate.queryForObject(
                "SELECT id FROM user_conversation WHERE conversation_id = ?",
                Long.class, conversationId);

        if (id == null) {
            // 如果不存在，创建新会话（这里假设user_id为系统默认值1，实际应该从认证上下文获取）
            jdbcTemplate.update(
                    "INSERT INTO user_conversation (user_id, conversation_id) VALUES (?, ?)",
                    1L, conversationId
            );
            id = jdbcTemplate.queryForObject(
                    "SELECT id FROM user_conversation WHERE conversation_id = ?",
                    Long.class, conversationId);
        }
        return id;
    }

    private RowMapper<Message> messageRowMapper() {
        return (rs, rowNum) -> {
            String type = rs.getString("message_type");
            String content = rs.getString("content");
            String metadataJson = rs.getString("metadata");

            Message message = MessageEntity.toMessage(MessageEntity.MessageType.valueOf(type), content);
            if (metadataJson != null) {
                try {
                    Map<String, Object> metadata = objectMapper.readValue(metadataJson, Map.class);
                    message.getMetadata().putAll(metadata);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException("Failed to parse message metadata", e);
                }
            }
            return message;
        };
    }

    private String serializeMetadata(Message message) {
        try {
            return objectMapper.writeValueAsString(message.getMetadata());
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize message metadata", e);
        }
    }
}
