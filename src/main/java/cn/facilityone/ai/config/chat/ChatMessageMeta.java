package cn.facilityone.ai.config.chat;

import cn.facilityone.ai.entity.AiChatResponseMeta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.constant.Constable;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatMessageMeta {
    private AiChatResponseMeta.ExtraAction type;
    private String remark;
    private String content;

    public Map<String, Object> toMap() {
        Map<String, ? extends Constable> map = Map.of("type", type, "remark", remark, "content", content);
        return Map.of("extraAction",  map);
    }
}
