package cn.facilityone.ai.config.chat;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 11:37
 */
@Configuration
public class ChatClientConfig {

    @Value("${spring.ai.openai.chat.options.intent-model:}")
    private String intentModel;

    @Value("${spring.ai.openai.chat.options.simple-model:}")
    private String simpleModel;

    @Value("${spring.ai.openai.chat.options.vl-model:}")
    private String vlModel;

    private final OpenAiChatModel openAiChatModel;

    @Autowired
    public ChatClientConfig(OpenAiChatModel openAiChatModel) {
        this.openAiChatModel = openAiChatModel;
    }

    @Bean
    @Primary
    public ChatClient chatClient() {
        return ChatClient.builder(openAiChatModel)
                .defaultSystem("你是费哲软件开发的AI助手，名字叫天枢")
                .defaultAdvisors(new SimpleLoggerAdvisor())
                .build();
    }

    @Bean("intentClient")
    public ChatClient intentClient() {
        OpenAiChatModel intentChatModel = openAiChatModel.mutate()
                .defaultOptions(OpenAiChatOptions.builder().model(intentModel).temperature(0.0).build())
                .build();
        return ChatClient.builder(intentChatModel)
                .defaultSystem("你是费哲软件开发的AI助手，名字叫天枢")
                .defaultAdvisors(new SimpleLoggerAdvisor())
                .build();
    }

    @Bean("vlClient")
    public ChatClient vlClient() {
        OpenAiChatModel intentChatModel = openAiChatModel.mutate()
                .defaultOptions(OpenAiChatOptions.builder().model(vlModel).build())
                .build();
        return ChatClient.builder(intentChatModel)
                .defaultAdvisors(new SimpleLoggerAdvisor())
                .build();
    }

    @Bean("simpleClient")
    public ChatClient simpleClient() {
        OpenAiChatModel intentChatModel = openAiChatModel.mutate()
                .defaultOptions(OpenAiChatOptions.builder().model(simpleModel).build())
                .build();
        return ChatClient.builder(intentChatModel)
                .defaultAdvisors(new SimpleLoggerAdvisor())
                .build();
    }

}