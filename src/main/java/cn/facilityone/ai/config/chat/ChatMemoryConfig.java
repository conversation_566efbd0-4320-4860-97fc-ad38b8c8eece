package cn.facilityone.ai.config.chat;

import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class ChatMemoryConfig {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Bean("chatMemory")
    public ChatMemory defaultChatMemory() {
        return new CustomMessageWindowChatMemory(getChatMemoryRepository(), 10);
    }

    public ChatMemoryRepository getChatMemoryRepository() {
        return new CustomChatMemoryRepository(jdbcTemplate.getDataSource());
    }
}
