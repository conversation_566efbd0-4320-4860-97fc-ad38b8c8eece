package cn.facilityone.ai.config.log;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Aspect
@Component
public class LogParamAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogParamAspect.class);

    @Around("@annotation(logParam)")
    public Object around(ProceedingJoinPoint point, LogParam logParam) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        String methodName = signature.getDeclaringType().getSimpleName() + "." + signature.getName();
        String[] parameterNames = signature.getParameterNames();
        Object[] args = point.getArgs();

        // 获取需要过滤的参数名称集合
        Set<String> excludeParamSet = new HashSet<>(Arrays.asList(logParam.excludeParams()));

        StringBuilder params = new StringBuilder();
        int paramCount = Math.min(parameterNames.length, args.length);

        for (int i = 0; i < paramCount; i++) {
            String paramName = parameterNames[i];

            if (i > 0) {
                params.append(", ");
            }

            params.append(paramName).append("=");

            // 如果参数名在过滤列表中，则显示为 ******
            if (excludeParamSet.contains(paramName)) {
                params.append("******");
            } else {
                params.append(args[i] == null ? "null" : formatParameterValue(args[i], excludeParamSet));
            }
        }
        logger.info("{}被调用,名称:{},参数:{}.", logParam.tag().getTagName(), methodName, params);

        Object result = point.proceed();

        ObjectMapper objectMapper = new ObjectMapper();
        logger.info("{}调用完成,名称:{},返回值:{}.", logParam.tag().getTagName(), methodName, logParam.printReturn() ? objectMapper.writeValueAsString(result) : "略");

        return result;
    }

    /**
     * 格式化参数值，支持对象字段级别的过滤
     */
    private String formatParameterValue(Object value, Set<String> excludeParamSet) {
        if (value == null) {
            return "null";
        }

        // 对于基本类型和字符串，直接返回toString()
        if (isPrimitiveOrWrapper(value.getClass()) || value instanceof String) {
            return value.toString();
        }

        // 对于复杂对象，检查是否需要过滤字段
        try {
            return formatObjectWithFieldFiltering(value, excludeParamSet);
        } catch (Exception e) {
            // 如果反射失败，返回原始toString()
            return value.toString();
        }
    }

    /**
     * 格式化对象，过滤指定字段
     */
    private String formatObjectWithFieldFiltering(Object obj, Set<String> excludeParamSet) {
        if (obj == null) {
            return "null";
        }

        Class<?> clazz = obj.getClass();
        StringBuilder sb = new StringBuilder();
        sb.append(clazz.getSimpleName()).append("(");

        Field[] fields = clazz.getDeclaredFields();
        boolean first = true;

        for (Field field : fields) {
            // 跳过静态字段和合成字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || field.isSynthetic()) {
                continue;
            }

            if (!first) {
                sb.append(", ");
            }

            String fieldName = field.getName();
            sb.append(fieldName).append("=");

            try {
                field.setAccessible(true);
                Object fieldValue = field.get(obj);

                // 如果字段名在过滤列表中，显示为 *
                if (excludeParamSet.contains(fieldName)) {
                    sb.append("*");
                } else {
                    sb.append(fieldValue == null ? "null" : fieldValue.toString());
                }
            } catch (IllegalAccessException e) {
                sb.append("?");
            }

            first = false;
        }

        sb.append(")");
        return sb.toString();
    }

    /**
     * 检查是否为基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == Boolean.class ||
               clazz == Byte.class ||
               clazz == Character.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class;
    }
}