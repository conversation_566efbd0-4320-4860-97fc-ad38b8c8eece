package cn.facilityone.ai.config.log;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogParam {

    enum TAG {
        METHOD("方法"),
        TOOL("工具");

        private final String tagName;

        TAG(String tagName) {
            this.tagName = tagName;
        }

        public String getTagName() {
            return tagName;
        }
    }

    /**
     * 是否打印返回值
     */
    boolean printReturn() default false;

    /**
     * 特殊名称
     */
    TAG tag() default TAG.METHOD;

    /**
     * 需要过滤（不打印数值）的参数名称数组
     */
    String[] excludeParams() default {};
}