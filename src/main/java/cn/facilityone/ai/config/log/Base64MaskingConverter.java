package cn.facilityone.ai.config.log;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Base64MaskingConverter extends ClassicConverter {

    // 优化的正则表达式，使用捕获组
    // 1. (data:image/([a-zA-Z]+);base64,) - 捕获整个header，并单独捕获图片类型(如jpeg, png)
    // 2. ([A-Za-z0-9+/=\s\r\n]+) - 捕获所有Base64字符，包括可能存在的换行符
    private static final Pattern BASE64_URI_PATTERN =
            Pattern.compile("(data:image/([a-zA-Z]+);base64,)([A-Za-z0-9+/=\\s\\r\\n]+)");

    @Override
    public String convert(ILoggingEvent event) {
        String message = event.getFormattedMessage();
        if (message == null || !message.contains("data:image")) {
            // 快速失败：如果消息中连 "data:image" 都不包含，直接返回，避免正则开销
            return message;
        }

        Matcher matcher = BASE64_URI_PATTERN.matcher(message);
        if (!matcher.find()) {
            // 如果没有匹配项，也直接返回
            return message;
        }

        // 使用 StringBuffer 进行高效的字符串替换
        StringBuffer sb = new StringBuffer();
        do {
            String imageType = matcher.group(2); // 获取图片类型，如 "jpeg"
            String base64Data = matcher.group(3); // 获取纯Base64数据
            int dataLength = base64Data.length();

            // 创建一个更有意义的占位符
            String mask = String.format("[BASE64_DATA type:%s length:%d]", imageType, dataLength);

            // 将匹配到的整个部分替换为我们的占位符
            // 注意要对替换字符串中的特殊字符进行转义
            matcher.appendReplacement(sb, Matcher.quoteReplacement(mask));
        } while (matcher.find());

        matcher.appendTail(sb); // 将剩余的、未匹配的字符串追加到末尾
        return sb.toString();
    }
}