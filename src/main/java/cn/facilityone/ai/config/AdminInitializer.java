package cn.facilityone.ai.config;

import cn.facilityone.ai.service.biz.AdminUserBizService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AdminInitializer {

    @Value("${admin.default.username}")
    private String defaultUsername;

    @Value("${admin.default.password}")
    private String defaultPassword;

    @Bean
    public CommandLineRunner initializeAdmin(AdminUserBizService adminUserBizService) {
        return args -> {
            try {
                adminUserBizService.loadUserByUsername(defaultUsername);
            } catch (Exception e) {
                adminUserBizService.createAdminUser(defaultUsername, defaultPassword);
            }
        };
    }
}