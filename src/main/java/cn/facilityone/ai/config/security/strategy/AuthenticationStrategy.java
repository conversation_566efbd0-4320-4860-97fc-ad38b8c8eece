package cn.facilityone.ai.config.security.strategy;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;

/**
 * 认证策略接口
 * 定义不同认证方式的策略模式
 */
public interface AuthenticationStrategy {
    
    /**
     * 判断是否支持当前请求的认证方式
     * 
     * @param request HTTP请求
     * @return 是否支持
     */
    boolean supports(HttpServletRequest request);
    
    /**
     * 执行认证逻辑
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 认证结果
     * @throws Exception 认证异常
     */
    Authentication authenticate(HttpServletRequest request, HttpServletResponse response) throws Exception;
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
}
