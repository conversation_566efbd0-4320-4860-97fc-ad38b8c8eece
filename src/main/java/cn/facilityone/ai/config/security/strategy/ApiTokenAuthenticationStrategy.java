package cn.facilityone.ai.config.security.strategy;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationConverter;
import cn.facilityone.ai.config.security.openapi.ApiAuthenticationToken;
import cn.facilityone.ai.entity.RestResult;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.io.IOException;

/**
 * API Token认证策略实现
 */
@Slf4j
public class ApiTokenAuthenticationStrategy implements AuthenticationStrategy {

    private final AuthenticationManager apiAuthenticationManager;
    private final ApiAuthenticationConverter apiAuthenticationConverter;

    public ApiTokenAuthenticationStrategy(AuthenticationManager apiAuthenticationManager,
                                       ApiAuthenticationConverter apiAuthenticationConverter) {
        this.apiAuthenticationManager = apiAuthenticationManager;
        this.apiAuthenticationConverter = apiAuthenticationConverter;
    }

    @Override
    public boolean supports(HttpServletRequest request) {
        // 支持非管理员路径的API请求
        return !request.getRequestURI().startsWith("/admin/");
    }

    @Override
    public Authentication authenticate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.debug("执行API Token认证策略: {} {}", request.getMethod(), request.getRequestURI());

        try {
            Authentication authentication = apiAuthenticationConverter.convert(request);
            if (authentication == null) {
                log.warn("请求中未找到认证令牌 {} {}", request.getMethod(), request.getRequestURI());
                sendAuthenticationErrorResponse(response, "未提供有效的认证令牌");
                throw new RuntimeException("认证令牌缺失");
            }

            log.debug("尝试为用户进行令牌认证: {}",
                    ((ApiAuthenticationToken) authentication).getUser());

            Authentication result = apiAuthenticationManager.authenticate(authentication);
            log.info("API Token认证成功: {}", result.getName());
            return result;
        } catch (AuthenticationException e) {
            log.warn("API Token认证失败 {} {}: {}",
                    request.getMethod(), request.getRequestURI(), e.getMessage());
            sendAuthenticationErrorResponse(response, e.getMessage());
            throw e;
        }
    }

    @Override
    public String getStrategyName() {
        return "API Token认证策略";
    }

    /**
     * 发送认证错误响应
     */
    private void sendAuthenticationErrorResponse(HttpServletResponse response, String errorMessage) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");
        RestResult<Void> errorResult = RestResult.error(RestResult.Status.UNAUTHORIZED.getCode(), "认证失败：" + errorMessage);
        response.getWriter().write(JSONUtil.toJsonStr(errorResult));
    }
}
