package cn.facilityone.ai.config.security.openapi;

import cn.facilityone.ai.entity.ContentInfo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;

import java.io.Serial;
import java.util.Collection;

// 使用 AbstractAuthenticationToken 更方便，它已经实现了部分 Authentication 接口的方法
public class ApiAuthenticationToken extends AbstractAuthenticationToken {

    @Serial
    private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;

    // 在认证前，principal 是 token 字符串
    // 在认证后，principal 是代表用户的对象 (例如 UserDetails 或一个字符串，这里用字符串)
    private final Object principal;

    // 认证前 credentials 是 token 字符串
    // 认证后 credentials 应该被擦除或设置为 null，但为了简单，这里仍然保留 token 字符串
    private String token;

    // 用户标识符
    @Setter
    @Getter
    private String user;

    // 租户标识符
    @Setter
    @Getter
    private String tenantId;

    // 认证后，上下文对象
    @Setter
    @Getter
    private ContentInfo contentInfo;

    // 认证前构造函数
    public ApiAuthenticationToken(String token, String user, String tenantId) {
        super(null); // 认证前没有权限
        this.principal = token;
        this.token = token;
        this.user = user;
        this.tenantId = tenantId;
        setAuthenticated(false); // 标记为未认证
    }

    // 认证后构造函数
    public ApiAuthenticationToken(Object principal, String token, String user, ContentInfo contentInfo,
                                  Collection<? extends GrantedAuthority> authorities) {
        super(authorities); // 认证后带有权限
        this.principal = principal;
        this.token = token;
        this.user = user;
        this.contentInfo = contentInfo;
        super.setAuthenticated(true); // 标记为已认证
    }

    @Override
    public Object getCredentials() {
        // 返回凭证，这里是 token 字符串
        return this.token;
    }

    @Override
    public Object getPrincipal() {
        // 返回主体信息
        return this.principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                    "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }
        super.setAuthenticated(false); // 只能通过带权限的构造函数设置为认证状态
    }

    @Override
    public void eraseCredentials() {
        // 认证成功后可以擦除敏感信息
        this.token = null;
    }

}
