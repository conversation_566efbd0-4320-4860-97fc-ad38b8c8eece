package cn.facilityone.ai.config.security.openapi;

import cn.facilityone.ai.config.security.SecurityProperties;
import cn.facilityone.ai.constant.CommonConstant;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationConverter;

public class ApiAuthenticationConverter implements AuthenticationConverter {

    private final SecurityProperties securityProperties;

    public ApiAuthenticationConverter(SecurityProperties securityProperties) {
        this.securityProperties = securityProperties;
    }

    @Override
    public Authentication convert(HttpServletRequest request) {
        String tokenHeader = securityProperties.getApi().getToken().getHeader();
        String userHeader = securityProperties.getApi().getToken().getUserHeader();
        String userHeaderOld = securityProperties.getApi().getToken().getUserHeaderOld();
        String tenantHeader = securityProperties.getApi().getToken().getTenantHeader();

        String authHeader = request.getHeader(tokenHeader);
        String user = request.getHeader(userHeader);
        String userOld = request.getHeader(userHeaderOld);
        String tenantId = request.getHeader(tenantHeader);
        if (StrUtil.isEmpty(user)) {
            user = userOld;
        }
        if (StrUtil.isBlank(tenantId)) {
            tenantId = CommonConstant.DEFAULT_TENANT_ID;
        }

        if (authHeader == null || authHeader.trim().isEmpty()) {
            return null;
        }

        // 去除Bearer前缀，直接使用token
        String token = authHeader;
        if (authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (token.trim().isEmpty()) {
            return null;
        }

        return new ApiAuthenticationToken(token, user, tenantId);
    }
}
