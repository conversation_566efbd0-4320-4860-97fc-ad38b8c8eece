package cn.facilityone.ai.config.security;

import cn.facilityone.ai.config.security.admin.SkipAuth;
import cn.facilityone.ai.config.security.strategy.AuthenticationStrategy;
import cn.facilityone.ai.entity.RestResult;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
public class UnifiedAuthenticationFilter extends OncePerRequestFilter {

    private final List<AuthenticationStrategy> authenticationStrategies;
    private final HandlerMapping handlerMapping;
    private final SecurityProperties securityProperties;

    public UnifiedAuthenticationFilter(List<AuthenticationStrategy> authenticationStrategies,
                                    HandlerMapping handlerMapping,
                                    SecurityProperties securityProperties) {
        this.authenticationStrategies = authenticationStrategies;
        this.handlerMapping = handlerMapping;
        this.securityProperties = securityProperties;
    }

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, 
                                  @NotNull HttpServletResponse response,
                                  @NotNull FilterChain filterChain) throws ServletException, IOException {

        String requestPath = request.getRequestURI();
        
        // 检查是否为白名单路径
        if (isWhitelistPath(requestPath)) {
            log.debug("跳过认证，白名单路径: {}", requestPath);
            filterChain.doFilter(request, response);
            return;
        }

        // 检查是否有SkipAuth注解
        if (hasSkipAuthAnnotation(request)) {
            log.info("跳过认证，方法标注了@SkipAuth: {}", requestPath);
            filterChain.doFilter(request, response);
            return;
        }

        // 查找合适的认证策略
        AuthenticationStrategy strategy = findAuthenticationStrategy(request);
        if (strategy != null) {
            try {
                log.debug("使用认证策略: {} 处理请求: {}", strategy.getStrategyName(), requestPath);
                var authentication = strategy.authenticate(request, response);
                if (authentication != null) {
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    filterChain.doFilter(request, response);
                }
            } catch (Exception e) {
                log.error("认证策略执行失败: {}, 错误: {}", strategy.getStrategyName(), e.getMessage());
                // 认证策略已经处理了错误响应，这里不需要额外处理
            }
        } else {
            log.warn("未找到合适的认证策略处理请求: {}", requestPath);
            sendAuthenticationErrorResponse(response, "不支持的认证方式");
        }
    }

    /**
     * 查找合适的认证策略
     */
    private AuthenticationStrategy findAuthenticationStrategy(HttpServletRequest request) {
        return authenticationStrategies.stream()
                .filter(strategy -> strategy.supports(request))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查是否为白名单路径
     */
    private boolean isWhitelistPath(String requestPath) {
        // 检查配置的白名单路径
        for (String whitelistPath : securityProperties.getApi().getWhitelist().getPaths()) {
            if (whitelistPath.contains("**")) {
                // 处理通配符路径
                String pattern = whitelistPath.replace("**", ".*");
                if (requestPath.matches(pattern)) {
                    return true;
                }
            } else {
                // 处理具体路径
                if (requestPath.equals(whitelistPath)) {
                    return true;
                }
            }
        }
        
        // 检查特殊路径
        return requestPath.startsWith("/file/download/temp/") || 
               requestPath.startsWith("/error");
    }

    /**
     * 检查是否有SkipAuth注解
     */
    private boolean hasSkipAuthAnnotation(HttpServletRequest request) {
        try {
            HandlerExecutionChain handlerChain = handlerMapping.getHandler(request);
            if (handlerChain != null && handlerChain.getHandler() instanceof HandlerMethod handlerMethod) {
                if (handlerMethod.hasMethodAnnotation(SkipAuth.class) || 
                    handlerMethod.getBeanType().isAnnotationPresent(SkipAuth.class)) {
                    // 设置匿名认证
                    SecurityContextHolder.getContext().setAuthentication(
                        new UsernamePasswordAuthenticationToken("anonymous", null, 
                            Collections.singletonList(new SimpleGrantedAuthority("ROLE_ANONYMOUS")))
                    );
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("检查SkipAuth注解时发生异常: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 发送认证错误响应
     */
    private void sendAuthenticationErrorResponse(HttpServletResponse response, String errorMessage) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");
        RestResult<Void> errorResult = RestResult.error(RestResult.Status.UNAUTHORIZED.getCode(), errorMessage);
        response.getWriter().write(JSONUtil.toJsonStr(errorResult));
    }
}
