package cn.facilityone.ai.config.security;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
public class SecurityProperties {

    private Api api = new Api();
    private Unified unified = new Unified();

    @Data
    public static class Api {
        private Token token = new Token();
        private Whitelist whitelist = new Whitelist();

        @Data
        public static class Token {
            private String header = "Authorization";
            private String prefix = "Bearer ";
            private String userHeader = "X-User";
            private String userHeaderOld = "User";
            private String tenantHeader = "X-Tenant-Id";
        }

        @Data
        public static class Whitelist {
            private List<String> paths = List.of("/admin/auth/login", "/error");
        }
    }

    @Data
    public static class Unified {
        /**
         * 是否启用统一认证
         */
        private boolean enabled = true;
        
        /**
         * 认证失败时的错误消息
         */
        private String authenticationFailedMessage = "认证失败";
        
        /**
         * 令牌缺失时的错误消息
         */
        private String tokenMissingMessage = "未提供有效的认证令牌";
        
        /**
         * 用户信息加载失败时的错误消息
         */
        private String userLoadFailedMessage = "用户信息加载失败";
    }
}