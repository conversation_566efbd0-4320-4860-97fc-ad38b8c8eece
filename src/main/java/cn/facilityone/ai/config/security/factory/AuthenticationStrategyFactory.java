package cn.facilityone.ai.config.security.factory;

import cn.facilityone.ai.config.security.strategy.AuthenticationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 认证策略工厂
 * 用于管理和创建认证策略
 */
@Slf4j
@Component
public class AuthenticationStrategyFactory {

    private final Map<String, AuthenticationStrategy> strategyMap;

    @Autowired
    public AuthenticationStrategyFactory(List<AuthenticationStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(
                        AuthenticationStrategy::getStrategyName,
                        Function.identity()
                ));
        
        log.info("已注册 {} 个认证策略: {}", 
                strategies.size(), 
                strategies.stream()
                        .map(AuthenticationStrategy::getStrategyName)
                        .collect(Collectors.joining(", ")));
    }

    /**
     * 根据策略名称获取认证策略
     */
    public AuthenticationStrategy getStrategy(String strategyName) {
        return strategyMap.get(strategyName);
    }

    /**
     * 获取所有认证策略
     */
    public List<AuthenticationStrategy> getAllStrategies() {
        return List.copyOf(strategyMap.values());
    }

    /**
     * 检查策略是否存在
     */
    public boolean hasStrategy(String strategyName) {
        return strategyMap.containsKey(strategyName);
    }

    /**
     * 获取策略数量
     */
    public int getStrategyCount() {
        return strategyMap.size();
    }
}
