package cn.facilityone.ai.config.security;

import cn.facilityone.ai.config.security.openapi.ApiAuthenticationConverter;
import cn.facilityone.ai.config.security.openapi.ApiAuthenticationProvider;
import cn.facilityone.ai.config.security.strategy.ApiTokenAuthenticationStrategy;
import cn.facilityone.ai.config.security.strategy.JwtAuthenticationStrategy;
import cn.facilityone.ai.service.db.AppService;
import cn.facilityone.ai.service.db.AppTokenService;
import cn.facilityone.ai.service.db.UserService;
import cn.facilityone.ai.service.flow.FmFlowService;
import jakarta.servlet.DispatcherType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.List;

/**
 * 统一安全配置
 * 使用UnifiedAuthenticationFilter处理所有认证逻辑
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private AppTokenService appTokenService;
    @Autowired
    private UserService userService;
    @Autowired
    private AppService appService;
    @Autowired
    private FmFlowService fmFlowService;
    @Autowired
    private UserDetailsService userDetailsService;
    @Autowired
    private SecurityProperties securityProperties;

    @Bean
    public ApiAuthenticationProvider apiAuthenticationProvider() {
        return new ApiAuthenticationProvider(appTokenService, userService, appService, fmFlowService);
    }

    @Bean
    public AuthenticationManager apiAuthenticationManager() {
        return new ProviderManager(apiAuthenticationProvider());
    }

    @Bean
    public ApiAuthenticationConverter apiAuthenticationConverter() {
        return new ApiAuthenticationConverter(securityProperties);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean("adminAuthenticationManager")
    public AuthenticationManager adminAuthenticationManager() {
        return new ProviderManager(daoAuthenticationProvider());
    }

    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider(userDetailsService);
        provider.setPasswordEncoder(passwordEncoder());
        return provider;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        String[] whitelistPaths = securityProperties.getApi().getWhitelist().getPaths().toArray(new String[0]);

        http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(cors -> cors.configurationSource(request -> {
                    var configuration = new CorsConfiguration();
                    configuration.setAllowedOrigins(List.of("*"));
                    configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
                    configuration.setAllowedHeaders(List.of("*"));
                    configuration.setAllowCredentials(false);
                    configuration.setMaxAge(600L);
                    return configuration;
                }))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(unifiedAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(whitelistPaths).permitAll()
                        .requestMatchers("/file/download/temp/**").permitAll()
                        .dispatcherTypeMatchers(DispatcherType.ASYNC, DispatcherType.ERROR).permitAll()
                        .requestMatchers("/flow/chat-stream").authenticated()
                        .anyRequest().authenticated()
                )
                .exceptionHandling(exceptions -> exceptions
                        .authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED))
                );

        return http.build();
    }

    @Bean
    public UnifiedAuthenticationFilter unifiedAuthenticationFilter() {
        return new UnifiedAuthenticationFilter(
                authenticationStrategies(),
                handlerMapping(),
                securityProperties
        );
    }

    @Bean
    public List<cn.facilityone.ai.config.security.strategy.AuthenticationStrategy> authenticationStrategies() {
        return List.of(
                new JwtAuthenticationStrategy(jwtUtils(), userDetailsService),
                new ApiTokenAuthenticationStrategy(apiAuthenticationManager(), apiAuthenticationConverter())
        );
    }

    @Bean
    public cn.facilityone.ai.config.security.admin.JwtUtils jwtUtils() {
        return new cn.facilityone.ai.config.security.admin.JwtUtils();
    }

    @Bean
    public HandlerMapping handlerMapping() {
        return new RequestMappingHandlerMapping();
    }
}
