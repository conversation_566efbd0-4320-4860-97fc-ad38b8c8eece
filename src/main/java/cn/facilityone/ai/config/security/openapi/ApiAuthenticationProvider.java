package cn.facilityone.ai.config.security.openapi;

import cn.facilityone.ai.entity.AppEntity;
import cn.facilityone.ai.entity.AppTokenEntity;
import cn.facilityone.ai.entity.ContentInfo;
import cn.facilityone.ai.entity.UserEntity;
import cn.facilityone.ai.service.db.AppService;
import cn.facilityone.ai.service.db.AppTokenService;
import cn.facilityone.ai.service.db.UserService;
import cn.facilityone.ai.service.flow.FmFlowService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;

@Slf4j
public class ApiAuthenticationProvider implements AuthenticationProvider {

    private final AppTokenService appTokenService;
    private final UserService userService;
    private final AppService appService;
    private final FmFlowService fmFlowService;

    public ApiAuthenticationProvider(AppTokenService appTokenService, UserService userService,
                                     AppService appService, FmFlowService fmFlowService) {
        if (appTokenService == null || userService == null || appService == null) {
            throw new IllegalArgumentException("AppTokenService && UserService cannot be null");
        }
        this.appTokenService = appTokenService;
        this.userService = userService;
        this.appService = appService;
        this.fmFlowService = fmFlowService;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        ApiAuthenticationToken bearerTokenAuth = (ApiAuthenticationToken) authentication;
        String token = (String) bearerTokenAuth.getCredentials();
        String user = bearerTokenAuth.getUser();
        String tenantId = bearerTokenAuth.getTenantId();

        // 直接进行token验证，不需要检查白名单（Spring Security已处理）
        AppTokenEntity tokenEntity = appTokenService.findValidToken(token);
        if (tokenEntity == null) {
            throw new InsufficientAuthenticationException("无效的Token");
        }

        ContentInfo contentInfo = new ContentInfo();
        contentInfo.setAppId(tokenEntity.getAppId());
        contentInfo.setTokenId(tokenEntity.getId());
        contentInfo.setCurrentTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        // 获取APP信息
        if (tokenEntity.getAppId() != null) {
            AppEntity appEntity = appService.getById(tokenEntity.getAppId());
            contentInfo.setAppCode(appEntity.getAppCode());
        }

        // 如果提供了 user 字段，则获取对应的 userId
        if (StrUtil.isNotBlank(user)) {
            UserEntity userEntity = userService.getOne(new LambdaQueryWrapper<UserEntity>()
                    .eq(UserEntity::getUsername, user)
                    .eq(UserEntity::getAppId, tokenEntity.getAppId()));
            // 不存在则创建用户
            boolean isNew = false;
            if (userEntity == null) {
                userEntity = new UserEntity();
                userEntity.setUsername(user);
                userEntity.setAppId(tokenEntity.getAppId());
                userEntity.setUserUuid(IdUtil.randomUUID());
                userEntity.setLastLoginAt(LocalDateTime.now());
                userService.save(userEntity);
                isNew = true;
            }
            contentInfo.setUserId(userEntity.getId());
            contentInfo.setUserUuid(userEntity.getUserUuid());
            contentInfo.setUsername(userEntity.getUsername());
            contentInfo.setUserStatus(userEntity.getUserStatus());
            if (!isNew && userEntity.getUserStatus()) {
                // 更新最后访问时间
                userService.update(new LambdaUpdateWrapper<UserEntity>()
                        .set(UserEntity::getLastLoginAt, LocalDateTime.now())
                        .eq(UserEntity::getId, userEntity.getId())
                );
            }
        } else {
            throw new InsufficientAuthenticationException("用户信息缺失(未传递user字段)");
        }
        if (contentInfo.getUserId() == null) {
            throw new InsufficientAuthenticationException("用户不存在");
        }
        if (contentInfo.getUserStatus() != null && !contentInfo.getUserStatus()) {
            throw new InsufficientAuthenticationException("用户已被禁用");
        }
        // 设置租户ID
        if (StrUtil.isNotBlank(tenantId)) {
            contentInfo.setTenantId(tenantId);
        }

        // 解析FM上下文信息
        String appCode = contentInfo.getAppCode();
        if (appCode.startsWith("fm-")) {
            fmFlowService.initFmContentInfo(contentInfo);
        }

        return new ApiAuthenticationToken(
                "valid_bearer_user", token, user, contentInfo,
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_API")));
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return ApiAuthenticationToken.class.isAssignableFrom(authentication);
    }

}
