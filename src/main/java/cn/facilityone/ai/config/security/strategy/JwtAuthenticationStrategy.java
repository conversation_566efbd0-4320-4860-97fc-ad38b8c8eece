package cn.facilityone.ai.config.security.strategy;

import cn.facilityone.ai.config.security.admin.JwtUtils;
import cn.facilityone.ai.entity.RestResult;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;

import java.io.IOException;

/**
 * JWT认证策略实现
 */
@Slf4j
public class JwtAuthenticationStrategy implements AuthenticationStrategy {

    private final JwtUtils jwtUtils;
    private final UserDetailsService userDetailsService;

    public JwtAuthenticationStrategy(JwtUtils jwtUtils, UserDetailsService userDetailsService) {
        this.jwtUtils = jwtUtils;
        this.userDetailsService = userDetailsService;
    }

    @Override
    public boolean supports(HttpServletRequest request) {
        return request.getRequestURI().startsWith("/admin/");
    }

    @Override
    public Authentication authenticate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.debug("执行JWT认证策略: {} {}", request.getMethod(), request.getRequestURI());

        String token = extractJwtTokenFromRequest(request);
        if (token != null && jwtUtils.validateToken(token)) {
            String username = jwtUtils.getUsernameFromToken(token);
            log.debug("发现有效的JWT令牌，用户: {}", username);

            try {
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                log.debug("JWT认证成功，用户: {}", username);
                return authToken;
            } catch (Exception e) {
                log.error("加载用户详情失败: {}", e.getMessage());
                sendAuthenticationErrorResponse(response, "用户信息加载失败");
                throw e;
            }
        } else {
            log.warn("请求中未找到有效的JWT令牌");
            sendAuthenticationErrorResponse(response, "认证失败：未找到有效的JWT令牌");
            throw new RuntimeException("JWT令牌无效");
        }
    }

    @Override
    public String getStrategyName() {
        return "JWT认证策略";
    }

    /**
     * 从请求中提取JWT令牌
     */
    private String extractJwtTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 发送认证错误响应
     */
    private void sendAuthenticationErrorResponse(HttpServletResponse response, String errorMessage) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");
        RestResult<Void> errorResult = RestResult.error(RestResult.Status.UNAUTHORIZED.getCode(), errorMessage);
        response.getWriter().write(JSONUtil.toJsonStr(errorResult));
    }
}
