package cn.facilityone.ai.constant;

import cn.facilityone.ai.dto.FmAppIntentRouterDTO;

import java.util.*;

public class FmFlowConstant {

    /**
     * FM-APP-混合工作流，操作问题-跳转识别，路由匹配
     * 创建工单 → CW, 创建需求 → CR, 待处理工单 → PW, 待处理需求 → PR, 工单查询 → WQ, 需求查询 → RQ, 报表 → RP, 巡检任务 → PT, 巡检查询 → PQ, 新建物资 → MI, 入库 → IN, 出库 → OT, 移库 → MV, 库存预定 → RS, 库存查询 → IQ, 其他 → O
     */
    public static final Set<FmAppIntentRouterDTO> APP_INTENT_ROUTER_SET = Set.of(
            new FmAppIntentRouterDTO("CW", "创建工单", "m-wo-create"),
            new FmAppIntentRouterDTO("CR", "创建需求", "m-requirement-create"),
            new FmAppIntentRouterDTO("PW", "待处理工单", "m-wo-process"),
            new FmAppIntentRouterDTO("PR", "待处理需求", "m-requirement-process"),
            new FmAppIntentRouterDTO("WQ", "工单查询", "m-wo-query"),
            new FmAppIntentRouterDTO("RQ", "需求查询", "m-requirement-query"),
            new FmAppIntentRouterDTO("RP", "报表", "m-chart"),
//            new FmAppIntentRouterDTO("PT", "巡检任务", "m-patrol-task"),
            new FmAppIntentRouterDTO("PQ", "巡检查询", "m-patrol-query"),
            new FmAppIntentRouterDTO("MI", "新建物资", "m-inventory-create"),
            new FmAppIntentRouterDTO("IN", "入库", "m-inventory-in"),
            new FmAppIntentRouterDTO("OT", "出库", "m-inventory-out"),
            new FmAppIntentRouterDTO("MV", "移库", "m-inventory-move"),
            new FmAppIntentRouterDTO("RS", "库存预定", "m-inventory-reserve"),
            new FmAppIntentRouterDTO("IQ", "库存查询", "m-inventory-query"),
            new FmAppIntentRouterDTO("O", "其他", "")
    );

    /**
     * FM-APP-混合工作流，操作问题-进度查询，提示列表
     */
    public static final String WO_OR_REQUIRE_CHOOSE_SUGGESTION = """
            {
                "type": "aiList",
                "direction": "Horizontal",
                "span": "2",
                "options": [
                {
                    "type": "aiSmallEntry",
                    "buttonLabel": "工单",
                    "action": "工单"
                },
                {
                    "type": "aiSmallEntry",
                    "buttonLabel": "需求",
                    "action": "需求"
                }]
            }""";

    /**
     * FM-APP-混合工作流，操作问题，提示问题列表
     */
    public static final List<String> OPERATION_QUESTION_SUGGESTION_LIST = new ArrayList<>(List.of(
            "如何创建工单？", "如何创建需求？", "如何查询待处理的工单？", "如何查询待处理的需求？", "如何查看工单报表？",
            "如何进行巡检任务？", "如何查询巡检任务？",
            "如何创建物资？", "如何进行物资入库？", "如何进行物资出库？", "如何进行物资移库？", "如何预定物资？", "如何查询物资库存？"
    ));

    /**
     * FM-APP-混合工作流，操作问题，提示问题格式模板
     */
    public static final String OPERATION_QUESTION_SUGGESTION_MSG_MODEL = """
            {
                "type": "aiList",
                "direction": "Vertical",
                "span": "1",
                "options": [
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "{}"
                    },
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "{}"
                    },
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "{}"
                    },
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "{}"
                    }
                ]
            }
            """;

    /**
     * FM-H5-提报需求，快速报修欢迎语
     */
    public static final String WELCOME_MESSAGE_REQUIREMENT_TEMPLATE = """
            <p>为了更好地帮您处理报修，请详细描述问题的具体信息：</p>
            <hr>
            <p><strong>联系人、联系方式</strong>：请输入您或者报修人的姓名和电话</p>
            <p><strong>报修地点</strong>：问题发生具体位置（大厦/区域、楼层、房间）</p>
            <p><strong>报修内容</strong>：请描述需要报修的具体问题或故障现象。</p>
            <p><strong>其他信息</strong>：例如预约开始时间、预约结束时间等补充信息。</p>
            <p>例如："xx大厦4层南侧走廊新风口异响，麻烦在今天18点下班前排查维修。"</p>
            """;

    /**
     * FM-APP-混合工作流，开场白快捷操作
     */
    public static final String OPENING_REMARKS_QUICK_OPERATION = """
            {
                "type": "aiList",
                "direction": "Vertical",
                "options": [
                    {
                        "type": "aiEntry",
                        "mainTitle": "操作问题",
                        "subTitle": "帮您解答系统操作相关的问题",
                        "action": "send"
                    },
                    {
                        "type": "aiEntry",
                        "mainTitle": "一键报单",
                        "subTitle": "输入问题描述，为您自动填报工单",
                        "action": "send"
                    },
                    {
                        "type": "aiEntry",
                        "mainTitle": "快速报修",
                        "subTitle": "输入需求描述，为您自动填报需求",
                        "action": "send"
                    },
                    {
                        "type": "aiEntry",
                        "mainTitle": "进度查询",
                        "subTitle": "帮您追踪工单、需求的解决进度",
                        "action": "send"
                    }
                ]
            }
            """;

    /**
     * COE-WEB-多FM演示环境-外层-客服&路径跳转&报表分析，操作问题-跳转识别，路由匹配
     * 按新逻辑：R 创建需求，F 创建工单，C 需求查询，S 工单查询，W 待处理工单，Q 待处理需求，I 巡检计划，T 巡检任务查询，O 其它问题
     */
    public static final Set<FmAppIntentRouterDTO> DEMO_WEB_OUT_FM_INTENT_ROUTER_SET = Set.of(
            new FmAppIntentRouterDTO("F", "创建工单", "main/home/<USER>"),
            new FmAppIntentRouterDTO("R", "创建需求", "main/home/<USER>"),
            new FmAppIntentRouterDTO("W", "待处理工单", "main/home/<USER>"),
            new FmAppIntentRouterDTO("Q", "待处理需求", "main/home/<USER>"),
            new FmAppIntentRouterDTO("S", "工单查询", "main/home/<USER>"),
            new FmAppIntentRouterDTO("C", "需求查询", "main/home/<USER>"),
            new FmAppIntentRouterDTO("I", "巡检计划", "main/home/<USER>"),
            new FmAppIntentRouterDTO("T", "巡检任务查询", "main/home/<USER>"),
            new FmAppIntentRouterDTO("O", "其他", "")
    );

    /**
     * COE-WEB-多FM演示环境-外层-客服&路径跳转&报表分析，操作问题，提示问题列表
     */
    public static final List<String> DEMO_WEB_OUT_OPERATION_QUESTION_SUGGESTION_LIST = new ArrayList<>(List.of(
            "如何创建工单？", "如何创建需求？", "如何查询待处理的工单？", "如何查询待处理的需求？", "如何查询工单进度？",
            "如何查询需求进度？", "如何进行巡检任务？", "如何查询巡检任务？"
    ));

    /**
     * COE-WEB-多FM演示环境-外层-客服&路径跳转&报表分析，模块跳转链接
     */
    public static final String DEMO_WEB_OUT_MODULE_JUMP_LINK_TEMPLATE = """
            {
                "options": [
                {
                    "type": "aiSmallEntry",
                    "buttonLabel": "跳转示例-百度",
                    "action": "jump",
                    "actionExtra": "https://www.baidu.com?"
                },
                {
                    "type": "aiSmallEntry",
                    "buttonLabel": "跳转示例-qq",
                    "action": "jump",
                    "actionExtra": "https://www.qq.com?"
                }]
            }
            """;

    /**
     * 演示环境-外层-WEB，操作问题，提示问题格式模板
     */
    public static final String DEMO_WEB_OUT_OPERATION_QUESTION_SUGGESTION_MSG_MODEL = """
            {
                "options": [
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "send",
                        "actionExtra": "{}"
                    },
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "send",
                        "actionExtra": "{}"
                    },
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "send",
                        "actionExtra": "{}"
                    },
                    {
                        "type": "aiSmallEntry",
                        "buttonLabel": "{}",
                        "action": "send",
                        "actionExtra": "{}"
                    }
                ]
            }
            """;

}
