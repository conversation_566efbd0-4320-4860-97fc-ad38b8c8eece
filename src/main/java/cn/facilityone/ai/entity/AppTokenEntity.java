package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应用Token表实体类
 */
@Data
@TableName("app_token")
public class AppTokenEntity {

    /**
     * Token的唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的 app 表的 id
     */
    @TableField(value = "app_id")
    private Long appId;

    /**
     * 鉴权Token字符串
     */
    @TableField(value = "token")
    private String token;

    /**
     * Token过期时间 (NULL表示永不过期)
     */
    @TableField(value = "expires_at")
    private LocalDateTime expiresAt;

    /**
     * Token状态 (0:失效, 1:有效)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
}