package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 后台管理员用户表
 */
@Data
@TableName("admin_user")
public class AdminUserEntity {
    /**
     * 主键 (内部使用)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 通用唯一识别码 (外部API使用)
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 管理员用户名
     */
    @TableField(value = "username")
    private String username;

    /**
     * 加密密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * 真实姓名
     */
    @TableField(value = "real_name")
    private String realName;

    /**
     * 状态 (0:禁用, 1:启用)
     */
    @TableField(value = "status")
    private Boolean status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间 (自动更新)
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 最后登录时间
     */
    @TableField(value = "last_login_at")
    private LocalDateTime lastLoginAt;
} 