/*
 * Copyright 2006-2019 FacilityONE Inc. All Rights Reserved
 * <p>
 * 注意：
 * 本软件内容仅限于费哲软件内部传阅，禁止外泄以及用于其他商业目的
 * 费哲软件(FacilityONE) : www.facilityone.cn
 */

package cn.facilityone.ai.entity;

import kotlin.Result;
import lombok.Data;
import lombok.Getter;

import static cn.facilityone.ai.entity.RestResult.Status.SUCCESS;

/**
 * 通用的对外开放接口Controller返回值包装类
 *
 * <AUTHOR>
 * @date 2022/12/22
 * @since 1.0
 **/
@Data
public class RestResult<T> {

    @Getter
    public enum Status {
        SUCCESS("SUCCESS", 200), FAIL("FAIL", 500),
        NOT_FOUND("NOT_FOUND", 404), UNAUTHORIZED("UNAUTHORIZED", 401),
        FORBIDDEN("FORBIDDEN", 403), PARAM_ERROR("PARAM_ERROR", 400);

        private final String message;
        private final int code;

        Status(String message, int code) {
            this.message = message;
            this.code = code;
        }

    }

    private String code = "200";
    private String message = "";
    private T data = null;

    public RestResult() {
    }

    public RestResult(String code) {
        this.code = code;
    }

    public RestResult(Status status) {
        this.code = String.valueOf(status.code);
        this.message = status.message;
    }

    public RestResult(Status status, T data) {
        this.code = String.valueOf(status.code);
        this.message = status.message;
        this.data = data;
    }

    public RestResult(String message, T data) {
        this.message = message;
        this.data = data;
    }

    public RestResult(Status status, String message, T data) {
        this.code = String.valueOf(status.code);
        this.message = message;
        this.data = data;
    }

    public RestResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    public RestResult<T> setMessage(String message) {
        this.message = message;
        return this;
    }

    public RestResult<T> setCode(String code) {
        this.code = code;
        return this;
    }

    public RestResult<T> setCode(int code) {
        this.code = String.valueOf(code);;
        return this;
    }

    public static <T> RestResult<T> success(T data) {
        RestResult<T> result = new RestResult<>();
        result.setCode(SUCCESS.code);
        result.setMessage(SUCCESS.message);
        result.setData(data);
        return result;
    }

    public static <T> RestResult<T> error(int code, String message) {
        RestResult<T> result = new RestResult<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}
