package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("file")
public class FileEntity {

    public enum FileType {
        IMAGE,
        VIDEO,
        AUDIO,
        DOCUMENT,
        OTHER
    }

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "name")
    private String name;

    @TableField(value = "path")
    private String path;

    @TableField(value = "hash")
    private String hash;

    @TableField(value = "type")
    private FileType type;

    @TableField(value = "upload_time")
    private Date uploadTime;

    @TableField(value = "is_thumb")
    private Boolean isThumb = false;

    /**
     * 父级文件ID(压缩后的图片等)
     */
    @TableField(value = "parent_file_id")
    private Long parentFileId;

    /**
     * 关联的 app 表的 id
     */
    @TableField(value = "app_id")
    private Long appId;

    /**
     * 关联的 user 表的 id
     */
    @TableField(value = "user_id")
    private Long userId;
}
