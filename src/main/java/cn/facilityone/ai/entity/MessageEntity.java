package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.ai.chat.messages.*;

import java.time.LocalDateTime;

/**
 * 消息表实体类
 */
@Data
@TableName("messages")
public class MessageEntity {

    public enum MessageType {
        system,
        user,
        assistant
    }

    /**
     * 唯一标识符，自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联到会话表
     */
    @TableField(value = "conversation_id")
    private Long conversationId;

    /**
     * 消息在会话中的顺序（从1开始）
     */
    @TableField(value = "sequence_number")
    private Integer sequenceNumber;

    /**
     * 消息类型：user, assistant, system, tool
     */
    @TableField(value = "message_type")
    private MessageType messageType;

    /**
     * 消息的文本内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 消息创建的时间戳
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 存储与消息相关的额外结构化数据（例如：模型信息、tokens 用量）的 JSON 字段
     */
    @TableField(value = "metadata")
    private String metadata;

    public static Message toMessage(MessageType messageType, String content) {
        Message message;
        switch (messageType) {
            case MessageType.user -> message = new UserMessage(content);
            case MessageType.assistant -> message = new AssistantMessage(content);
            case MessageType.system -> message = new SystemMessage(content);
            default -> throw new IncompatibleClassChangeError();
        }
        return message;
    }
}
