package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应用表实体类
 */
@Data
@TableName("app")
public class AppEntity {

    /**
     * 应用的唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用的编号，可以自定义
     */
    @TableField(value = "app_code")
    private String appCode;

    /**
     * 应用名称
     */
    @TableField(value = "app_name")
    private String appName;

    /**
     * 应用描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 应用状态 (0:禁用, 1:启用)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
}
