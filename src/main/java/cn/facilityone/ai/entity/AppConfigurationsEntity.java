package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应用配置中心表实体类
 */
@Data
@TableName("app_configurations")
public class AppConfigurationsEntity {

    /**
     * 主键ID (自增)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属应用的唯一数字标识符
     */
    @TableField(value = "app_id")
    private Long appId;

    /**
     * 租户ID，用于数据隔离
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 配置分组名
     */
    @TableField(value = "config_group")
    private String configGroup;

    /**
     * 配置项的Key
     */
    @TableField(value = "config_key")
    private String configKey;

    /**
     * 配置项的Value，使用TEXT以支持较长的配置内容
     */
    @TableField(value = "config_value")
    private String configValue;

    /**
     * 配置项的描述信息
     */
    @TableField(value = "description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
}