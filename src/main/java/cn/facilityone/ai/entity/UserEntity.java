package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户表实体类
 */
@Data
@TableName("\"user\"")
public class UserEntity {

    /**
     * 用户的唯一数字ID (主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户的全局唯一标识符 (例如 UUID)
     */
    @TableField(value = "user_uuid")
    private String userUuid;

    /**
     * 用户标识符（外部）
     */
    @TableField(value = "username")
    private String username;

    /**
     * 关联的 app 表的 id
     */
    @TableField(value = "app_id")
    private Long appId;

    /**
     * 用户状态 (0:禁用, 1:启用)
     */
    @TableField(value = "user_status")
    private Boolean userStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 最后登录时间
     */
    @TableField(value = "last_login_at")
    private LocalDateTime lastLoginAt;
}