package cn.facilityone.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户会话关联表
 */
@Data
@TableName("user_conversation")
@NoArgsConstructor
public class UserConversationEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 会话ID
     */
    @TableField(value = "conversation_id")
    private String conversationId;

    /**
     * 会话标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 删除标识
     */
    @TableField(value = "deleted")
    private Boolean deleted;

    public UserConversationEntity(Long userId, String conversationId, String title, String tenantId) {
        this.userId = userId;
        this.conversationId = conversationId;
        this.title = title;
        this.tenantId = tenantId;
    }
}