package cn.facilityone.ai.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 消息元数据，预定义
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiChatResponseMeta {

    public enum ExtraAction {
        NONE,
        // 截断消息
        CUT,
        // 对话返回结束
        END,
        // 直接跳转
        JUMP,
    }

    /**
     * 建议问题列表
     */
    List<SuggestedQuestion> suggestedQuestions;

    /**
     * 其它动作
     */
    private ExtraAction extraAction = ExtraAction.NONE;

    /**
     * 动作目标
     */
    private String actionTarget;

    public AiChatResponseMeta(List<SuggestedQuestion> suggestedQuestions) {
        this.suggestedQuestions = suggestedQuestions == null ? new ArrayList<>() : suggestedQuestions;
    }

    public AiChatResponseMeta(ExtraAction extraAction) {
        this.suggestedQuestions = suggestedQuestions == null ? new ArrayList<>() : suggestedQuestions;
        this.extraAction = extraAction == null ? ExtraAction.NONE : extraAction;
    }

    public AiChatResponseMeta(ExtraAction extraAction, String actionTarget) {
        this.suggestedQuestions = suggestedQuestions == null ? new ArrayList<>() : suggestedQuestions;
        this.extraAction = extraAction == null ? ExtraAction.NONE : extraAction;
        this.actionTarget = actionTarget;
    }
}
