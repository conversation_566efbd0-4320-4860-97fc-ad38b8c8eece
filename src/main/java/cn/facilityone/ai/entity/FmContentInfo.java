package cn.facilityone.ai.entity;

import cn.facilityone.ai.dto.FmProjectDTO;
import cn.facilityone.ai.properties.FmProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FmContentInfo {
    private Long fmUserId;
    private String fmOpenId;
    private String fmUserName;
    private String fmRealName;
    private String fmNickName;
    private String fmPhone;
    private String fmProjectName;
    private Long fmProjectId;
    private FmProperties.TenantConfig tenantConfig;
    private List<FmProjectDTO> projectList;

    @Override
    public String toString() {
        return "ContentInfoDTO{" + "userId(用户ID)=" + fmUserId + ", " +
                "openId(用户的openId)='" + ContentInfo.maskIfNull(fmOpenId) + '\'' + ", " +
                "userName(用户的登录名)='" + ContentInfo.maskIfNull(fmUserName) + '\'' + ", " +
                "realName(用户的真实姓名)='" + ContentInfo.maskIfNull(fmRealName) + '\'' + ", " +
                "nickName(用户的昵称)='" + ContentInfo.maskIfNull(fmNickName) + '\'' + ", " +
                "phone(用户的联系方式)='" + ContentInfo.maskIfNull(fmPhone) + '\'' + ", " +
                "projectName(当前项目名称)='" + ContentInfo.maskIfNull(fmProjectName) + '\'' + ", " +
                "projectId(当前项目ID)=" + fmProjectId + '\'' +
                '}';
    }

}