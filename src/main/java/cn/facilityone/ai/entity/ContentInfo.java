package cn.facilityone.ai.entity;

import lombok.Data;

@Data
public class ContentInfo {
    /**
     * 用户的唯一数字ID (主键)
     */
    private Long userId;

    /**
     * 当前调用使用的tokenId
     */
    private Long tokenId;

    /**
     * 当前会话的ID
     */
    private String conversationId;

    /**
     * 用户的全局唯一标识符 (例如 UUID)
     */
    private String userUuid;

    /**
     * 用户标识符（外部）
     */
    private String username;

    /**
     * 关联的 app 表的 id
     */
    private Long appId;

    /**
     * 关联的 app 表的 app_code
     */
    private String appCode;

    /**
     * 用户状态 (0:禁用, 1:启用)
     */
    private Boolean userStatus;

    /**
     * 当前时间
     */
    private String currentTime;

    /**
     * 租户ID
     */
    private String tenantId = "default";

    /**
     * FM系统上下文
     */
    private FmContentInfo fmContentInfo;

    // 处理 null 值，增强可读性
    public static String maskIfNull(String value) {
        return value == null ? "<null>" : value;
    }
}