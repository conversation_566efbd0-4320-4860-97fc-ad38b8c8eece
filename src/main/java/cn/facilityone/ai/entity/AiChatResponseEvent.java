package cn.facilityone.ai.entity;

import lombok.Getter;

/**
 * 事件
 * 已有类型：MESSAGE:返回文本内容;
 * 预定义类型：PING:ping事件;MESSAGE_END:消息结束;thought:返回思考内容;file:返回文件;tts_message:音频流;tts_end:音频流结束;error:错误;
 */
@Getter
public enum AiChatResponseEvent {
    MESSAGE("MESSAGE"),
    PING("PING"),
    MESSAGE_END("MESSAGE_END");

    private String event;

    AiChatResponseEvent(String event) {
        this.event = event;
    }
}
