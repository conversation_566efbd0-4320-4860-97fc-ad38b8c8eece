package cn.facilityone.ai.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天返回实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiChatResponse {

    /**
     * 事件
     */
    private AiChatResponseEvent event;
    /**
     * 消息ID
     */
    private String messageId;
    /**
     * 会话ID
     */
    private String conversationId;
    /**
     * 回复内容
     */
    private String text;
    /**
     * 消息元数据
     */
    private AiChatResponseMeta metadata = new AiChatResponseMeta();

    /**
     * 创建响应体
     */
    public static AiChatResponse createResponse(String msg, String conversationId) {
        return new AiChatResponse(AiChatResponseEvent.MESSAGE, "0", conversationId, msg, new AiChatResponseMeta());
    }

    public static AiChatResponse createResponse(String msg, String conversationId, AiChatResponseMeta metadata) {
        return new AiChatResponse(AiChatResponseEvent.MESSAGE, "0", conversationId, msg, metadata);
    }

    public static AiChatResponse createOperationResponse(String conversationId, AiChatResponseMeta.ExtraAction extraAction) {
        return new AiChatResponse(AiChatResponseEvent.MESSAGE, "0", conversationId, "", new AiChatResponseMeta(extraAction));
    }
}
