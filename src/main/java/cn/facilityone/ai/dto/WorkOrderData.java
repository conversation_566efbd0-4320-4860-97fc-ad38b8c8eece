package cn.facilityone.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 工单数据传输对象
 * 用于封装从CSV文件解析出的工单信息
 */
@Data
public class WorkOrderData {

    /**
     * 工单号
     */
    private String orderNumber;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "dd/MM/yy")
    private LocalDate createDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime createTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime publishTime;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 接单时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime acceptTime;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 部门
     */
    private String department;

    /**
     * 大厦
     */
    private String building;

    /**
     * 楼层
     */
    private String floor;

    /**
     * 房间
     */
    private String room;

    /**
     * 完整位置
     */
    private String fullLocation;

    /**
     * 描述
     */
    private String description;

    /**
     * 工作内容
     */
    private String workContent;

    /**
     * 工单状态
     */
    private String status;

    /**
     * 暂停原因
     */
    private String pauseReason;

    /**
     * 收费情况
     */
    private String chargeStatus;

    /**
     * 预估开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime estimatedStartTime;

    /**
     * 预估结束时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime estimatedEndTime;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime actualStartTime;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime actualEndTime;

    /**
     * 工作时长（分钟）
     */
    private Integer workDurationMinutes;

}