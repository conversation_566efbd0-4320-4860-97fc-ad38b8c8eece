package cn.facilityone.ai.dto;

import lombok.Data;

import java.util.Map;

/**
 * 离群数据统计信息传输对象
 * 包含离群数据的各种统计指标
 */
@Data
public class OutlierStatistics {

    /**
     * 总离群数据数量
     */
    private Integer totalOutlierCount;

    /**
     * 离群数据占比（百分比）
     */
    private Double outlierPercentage;

    /**
     * 按类型分组的离群数据统计
     * Key: 离群类型, Value: 数量
     */
    private Map<OutlierData.OutlierType, Integer> outlierTypeDistribution;

    /**
     * 按严重程度分组的离群数据统计
     * Key: 严重程度级别, Value: 数量
     */
    private Map<String, Integer> severityDistribution;

    /**
     * 处理时长离群统计
     */
    private ProcessingTimeOutlierStats processingTimeStats;

    /**
     * 响应时间离群统计
     */
    private ResponseTimeOutlierStats responseTimeStats;

    /**
     * 业务规则离群统计
     */
    private BusinessRuleOutlierStats businessRuleStats;

    /**
     * 处理时长离群统计详情
     */
    @Data
    public static class ProcessingTimeOutlierStats {
        /**
         * 超长处理时长工单数量
         */
        private Integer longProcessingCount;

        /**
         * 超短处理时长工单数量
         */
        private Integer shortProcessingCount;

        /**
         * 平均离群程度
         */
        private Double averageOutlierScore;

        /**
         * 最大处理时长（分钟）
         */
        private Integer maxProcessingTime;

        /**
         * 最小处理时长（分钟）
         */
        private Integer minProcessingTime;
    }

    /**
     * 响应时间离群统计详情
     */
    @Data
    public static class ResponseTimeOutlierStats {
        /**
         * 响应过慢工单数量
         */
        private Integer slowResponseCount;

        /**
         * 响应过快工单数量
         */
        private Integer fastResponseCount;

        /**
         * 平均响应时间（分钟）
         */
        private Double averageResponseTime;

        /**
         * 最大响应时间（分钟）
         */
        private Integer maxResponseTime;
    }

    /**
     * 业务规则离群统计详情
     */
    @Data
    public static class BusinessRuleOutlierStats {
        /**
         * 状态异常工单数量
         */
        private Integer statusAnomalyCount;

        /**
         * 优先级异常工单数量
         */
        private Integer priorityAnomalyCount;

        /**
         * 时间逻辑异常工单数量
         */
        private Integer timeLogicAnomalyCount;

        /**
         * 数据完整性异常工单数量
         */
        private Integer dataIntegrityAnomalyCount;
    }
}
