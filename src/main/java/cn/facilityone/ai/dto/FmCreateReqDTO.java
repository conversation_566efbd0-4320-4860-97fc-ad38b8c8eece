package cn.facilityone.ai.dto;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

import java.io.Serial;
import java.io.Serializable;

/**
 * FM创建请求DTO
 */
@Data
public class FmCreateReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 描述信息
     */
    @ToolParam(required = true, description = "需求描述")
    private String description;

    /**
     * 楼宇ID：若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配
     */
    @ToolParam(required = false, description = "楼宇ID，若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配")
    private Long buildingId;

    /**
     * 楼宇名称：若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配
     */
    @ToolParam(required = false, description = "楼宇名称，若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配")
    private String buildingName;

    /**
     * 楼层ID：若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配
     */
    @ToolParam(required = false, description = "楼层ID，若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配")
    private Long floorId;

    /**
     * 楼层名称：若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配
     */
    @ToolParam(required = false, description = "楼层名称，若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配")
    private String floorName;

    /**
     * 房间ID：若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配
     */
    @ToolParam(required = false, description = "房间ID，若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配")
    private Long roomId;

    /**
     * 房间名称：若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配
     */
    @ToolParam(required = false, description = "房间名称，若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配")
    private String roomName;

    /**
     * 请求类型ID：通过工具获取项目下的需求类型进行匹配
     */
    @ToolParam(required = true, description = "请求类型ID，通过工具获取项目下的需求类型进行匹配")
    private Long reqTypeId;

    /**
     * 请求类型名称：通过工具获取项目下的需求类型进行匹配
     */
    @ToolParam(required = true, description = "请求类型名称，通过工具获取项目下的需求类型进行匹配")
    private String reqTypeName;

    /**
     * 需求人姓名，用户未提供则为当前用户的姓名
     */
    @ToolParam(required = true, description = "需求人姓名，用户未提供则为当前用户的姓名，当前用户姓名为空时传递空字符串")
    private String name;

    /**
     * 需求人联系方式，用户未提及则为当前用户的联系方式，当前用户联系方式为空是传递空字符串
     */
    @ToolParam(required = true, description = "需求人联系方式，用户未提及则为当前用户的联系方式，当前用户联系方式为空时传递空字符串")
    private String phone;

    /**
     * 预约开始时间，格式：yyyy-MM-dd HH:mm，用户未提及则值为null
     */
    @ToolParam(required = false, description = "预约开始时间，格式：yyyy-MM-dd HH:mm，用户未提及时间则值为null，用户提及结束时间，则默认为当前时间")
    private String appointmentStartTime;

    /**
     * 预约结束时间，格式：yyyy-MM-dd HH:mm，用户未提及则值为null
     */
    @ToolParam(required = false, description = "预约结束时间，格式：yyyy-MM-dd HH:mm，用户未提及则值为null")
    private String appointmentEndTime;

    /**
     * 项目ID
     */
    @ToolParam(required = true, description = "项目ID")
    private Long projectId;

    /**
     * 项目名称
     */
    @ToolParam(required = true, description = "项目名称")
    private String projectName;

}