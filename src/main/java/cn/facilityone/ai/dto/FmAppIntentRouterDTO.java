package cn.facilityone.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Set;

@Data
@AllArgsConstructor
public class FmAppIntentRouterDTO {

    /**
     * 意图标识
     */
    private String intent;
    /**
     * 意图名称
     */
    private String name;
    /**
     * 路由
     */
    private String router;

    public static FmAppIntentRouterDTO indexByIntent(String intent, Set<FmAppIntentRouterDTO> set) {
        for (FmAppIntentRouterDTO item : set) {
            if (item.getIntent().equals(intent)) {
                return item;
            }
        }
        return null;
    }
}
