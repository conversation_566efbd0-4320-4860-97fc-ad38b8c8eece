package cn.facilityone.ai.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用于前端 Web 端小入口建议的数据结构
 * 期望序列化示例：
 * {
 *   "options": [
 *     {
 *       "type": "aiSmallEntry",
 *       "buttonLabel": "恒隆",
 *       "action": "jump",
 *       "actionExtra": "https://www.baidu.com"
 *     }
 *   ]
 * }
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmWebSuggestionDTO {

    public enum ActionType {
        /**
         * 跳转行为
         */
        jump,
        /**
         * 发送行为
         */
        send,
    }

    /**
     * 小入口选项列表
     */
    @JsonProperty("options")
    private List<Option> options;

    /**
     * 小入口选项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Option {

        /**
         * 组件类型，例如：aiSmallEntry
         */
        @JsonProperty("type")
        private String type = "aiSmallEntry";

        /**
         * 前端按钮展示的文案
         */
        @JsonProperty("buttonLabel")
        private String buttonLabel;

        /**
         * 行为类型，例如：jump
         */
        @JsonProperty("action")
        private ActionType action;

        /**
         * 行为的额外参数，例如跳转链接
         */
        @JsonProperty("actionExtra")
        private String actionExtra;
    }
}
