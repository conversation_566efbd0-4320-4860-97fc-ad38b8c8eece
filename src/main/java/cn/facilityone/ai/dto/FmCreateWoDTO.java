package cn.facilityone.ai.dto;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

import java.io.Serial;
import java.io.Serializable;

/**
 * FM创建请求DTO
 */
@Data
public class FmCreateWoDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 描述信息
     */
    @ToolParam(description = "工单描述")
    private String description;

    /**
     * 楼宇ID：若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配
     */
    @ToolParam(required = false, description = "楼宇ID，若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配")
    private Long buildingId;

    /**
     * 楼宇名称：若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配
     */
    @ToolParam(required = false, description = "楼宇名称，若用户提及位置信息，则通过工具获取项目下的楼宇进行匹配")
    private String buildingName;

    /**
     * 楼层ID：若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配
     */
    @ToolParam(required = false, description = "楼层ID，若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配")
    private Long floorId;

    /**
     * 楼层名称：若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配
     */
    @ToolParam(required = false, description = "楼层名称，若用户提及位置信息且已经匹配到楼宇，则通过工具获取楼宇下的楼层进行匹配")
    private String floorName;

    /**
     * 房间ID：若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配
     */
    @ToolParam(required = false, description = "房间ID，若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配")
    private Long roomId;

    /**
     * 房间名称：若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配
     */
    @ToolParam(required = false, description = "房间名称，若用户提及位置信息且已经匹配到楼层，则通过工具获取楼层下的房间进行匹配")
    private String roomName;

    /**
     * 服务类型ID：通过工具获取项目下的服务类型进行匹配
     */
    @ToolParam(description = "服务类型ID")
    private Long serviceTypeId;

    /**
     * 请求类型名称：通过工具获取项目下的服务类型进行匹配
     */
    @ToolParam(description = "服务类型名称")
    private String serviceTypeName;

    /**
     * 优先级ID
     */
    @ToolParam(description = "优先级ID")
    private Long priorityId;

    /**
     * 优先级名称
     */
    @ToolParam(description = "优先级名称")
    private String priorityName;

    /**
     * 工单流程ID
     */
    @ToolParam(description = "工单流程ID")
    private Long woProcessId;

    /**
     * 提报人姓名
     */
    @ToolParam(description = "提报人姓名")
    private String name;

    /**
     * 提报人联系方式
     */
    @ToolParam(description = "提报人联系方式")
    private String phone;

    /**
     * 操作人的人员ID（FM系统）
     */
    @ToolParam(description = "操作人的人员ID")
    private Long requestId;

    /**
     * 项目ID
     */
    @ToolParam(description = "项目ID")
    private Long projectId;

    /**
     * 项目名称
     */
    @ToolParam(description = "项目名称")
    private String projectName;

}