package cn.facilityone.ai.dto;

import lombok.Data;

import java.util.Map;

/**
 * 精简版工单统计分析结果数据传输对象
 * 专为AI分析优化，减少token使用量
 */
@Data
public class CompactWorkOrderAnalysisResult {

    /**
     * 工单总数
     */
    private Integer totalCount;

    /**
     * 状态分布统计（只保留前5个）
     */
    private Map<String, Integer> topStatusDistribution;

    /**
     * 优先级分布统计（只保留前3个）
     */
    private Map<String, Integer> topPriorityDistribution;

    /**
     * 服务类型分布统计（只保留前5个）
     */
    private Map<String, Integer> topServiceTypeDistribution;

    /**
     * 部门分布统计（只保留前5个）
     */
    private Map<String, Integer> topDepartmentDistribution;

    /**
     * 大厦分布统计（只保留前3个）
     */
    private Map<String, Integer> topBuildingDistribution;

    /**
     * 平均处理时长（分钟）
     */
    private Double averageProcessingTime;

    /**
     * 最长处理时长（分钟）
     */
    private Integer maxProcessingTime;

    /**
     * 最短处理时长（分钟）
     */
    private Integer minProcessingTime;

    /**
     * 完成率（百分比）
     */
    private Double completionRate;

    /**
     * 及时完成率（百分比）
     */
    private Double onTimeCompletionRate;

    /**
     * 收费工单数量
     */
    private Integer chargedOrderCount;

    /**
     * 免费工单数量
     */
    private Integer freeOrderCount;

    // ==================== 精简版离群数据分析 ====================

    /**
     * 离群数据总数量
     */
    private Integer totalOutlierCount;

    /**
     * 离群数据占比（百分比）
     */
    private Double outlierPercentage;

    /**
     * 处理时长离群工单数量
     */
    private Integer processingTimeOutlierCount;

    /**
     * 响应时间离群工单数量
     */
    private Integer responseTimeOutlierCount;

    /**
     * 业务规则离群工单数量
     */
    private Integer businessRuleOutlierCount;

    /**
     * 离群数据类型分布
     */
    private Map<String, Integer> outlierTypeDistribution;

    /**
     * 离群数据严重程度分布
     */
    private Map<String, Integer> outlierSeverityDistribution;

    /**
     * 离群数据摘要（不包含具体工单对象，只有统计信息）
     */
    private CompactOutlierSummary outlierSummary;

    /**
     * 精简版离群数据摘要
     */
    @Data
    public static class CompactOutlierSummary {
        /**
         * 最严重的处理时长异常（分钟）
         */
        private Integer maxProcessingTimeOutlier;

        /**
         * 最短的处理时长异常（分钟）
         */
        private Integer minProcessingTimeOutlier;

        /**
         * 最长的响应时间异常（分钟）
         */
        private Integer maxResponseTimeOutlier;

        /**
         * 业务规则异常类型统计
         */
        private Map<String, Integer> businessRuleAnomalyTypes;

        /**
         * 离群数据影响的主要部门（前3个）
         */
        private Map<String, Integer> topAffectedDepartments;
    }

    // ==================== 时间分布精简版 ====================

    /**
     * 工单创建高峰时段（小时）
     */
    private Map<String, Integer> peakHours;

    /**
     * 工单创建高峰月份
     */
    private Map<String, Integer> peakMonths;

    /**
     * 工单创建高峰星期
     */
    private Map<String, Integer> peakWeekdays;

    // ==================== 关键指标摘要 ====================

    /**
     * 关键指标摘要
     */
    private KeyMetricsSummary keyMetrics;

    /**
     * 关键指标摘要
     */
    @Data
    public static class KeyMetricsSummary {
        /**
         * 工作效率等级 (高/中/低)
         */
        private String efficiencyLevel;

        /**
         * 数据质量等级 (优/良/差)
         */
        private String dataQualityLevel;

        /**
         * 响应速度等级 (快/中/慢)
         */
        private String responseSpeedLevel;

        /**
         * 主要问题类型
         */
        private String primaryIssueType;

        /**
         * 改进优先级建议
         */
        private String improvementPriority;
    }
}
