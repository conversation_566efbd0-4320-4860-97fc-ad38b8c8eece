package cn.facilityone.ai.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 工单统计分析结果数据传输对象
 * 包含各种维度的统计数据
 */
@Data
public class WorkOrderAnalysisResult {

    /**
     * 工单总数
     */
    private Integer totalCount;

    /**
     * 状态分布统计
     * Key: 状态名称, Value: 数量
     */
    private Map<String, Integer> statusDistribution;

    /**
     * 优先级分布统计
     * Key: 优先级, Value: 数量
     */
    private Map<String, Integer> priorityDistribution;

    /**
     * 服务类型分布统计
     * Key: 服务类型, Value: 数量
     */
    private Map<String, Integer> serviceTypeDistribution;

    /**
     * 部门分布统计
     * Key: 部门名称, Value: 数量
     */
    private Map<String, Integer> departmentDistribution;

    /**
     * 大厦分布统计
     * Key: 大厦名称, Value: 数量
     */
    private Map<String, Integer> buildingDistribution;

    /**
     * 月度分布统计
     * Key: 月份(MM), Value: 数量
     */
    private Map<String, Integer> monthlyDistribution;

    /**
     * 周分布统计
     * Key: 星期几(1-7), Value: 数量
     */
    private Map<String, Integer> weeklyDistribution;

    /**
     * 小时分布统计
     * Key: 小时(HH), Value: 数量
     */
    private Map<String, Integer> hourlyDistribution;

    /**
     * 平均处理时长（分钟）
     */
    private Double averageProcessingTime;

    /**
     * 最长处理时长（分钟）
     */
    private Integer maxProcessingTime;

    /**
     * 最短处理时长（分钟）
     */
    private Integer minProcessingTime;

    /**
     * 完成率（百分比）
     */
    private Double completionRate;

    /**
     * 及时完成率（百分比）
     */
    private Double onTimeCompletionRate;

    /**
     * 收费工单数量
     */
    private Integer chargedOrderCount;

    /**
     * 免费工单数量
     */
    private Integer freeOrderCount;

    // ==================== 离群数据分析相关字段 ====================

    /**
     * 离群工单列表
     */
    private List<OutlierData> outlierWorkOrders;

    /**
     * 离群数据统计信息
     */
    private OutlierStatistics outlierStatistics;

    /**
     * 处理时长离群工单数量
     */
    private Integer processingTimeOutlierCount;

    /**
     * 响应时间离群工单数量
     */
    private Integer responseTimeOutlierCount;

    /**
     * 业务规则离群工单数量
     */
    private Integer businessRuleOutlierCount;

    /**
     * 离群数据总数量
     */
    private Integer totalOutlierCount;

    /**
     * 离群数据占比（百分比）
     */
    private Double outlierPercentage;

}