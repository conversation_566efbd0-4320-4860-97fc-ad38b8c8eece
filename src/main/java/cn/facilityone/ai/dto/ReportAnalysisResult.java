package cn.facilityone.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报表分析结果数据传输对象
 * 包含统计数据和AI分析结果
 */
@Data
public class ReportAnalysisResult {

    /**
     * 统计分析数据
     */
    private WorkOrderAnalysisResult statisticalData;

    /**
     * AI生成的分析报告
     */
    private String aiAnalysis;

    /**
     * 分析的文件名
     */
    private String fileName;

    /**
     * 分析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime analysisTime;

    /**
     * 分析的数据条数
     */
    private Integer dataCount;

    /**
     * 分析耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 分析状态
     */
    private String analysisStatus;

    /**
     * 备注信息
     */
    private String remarks;

}