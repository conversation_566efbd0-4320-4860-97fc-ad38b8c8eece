package cn.facilityone.ai.dto;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.List;

@Data
public class FmSearchReqOrderDTO {
    // 项目ID
    @ToolParam(description = "项目ID")
    private Long projectId;
    // 需求单编号（模糊搜索）
    @ToolParam(required = false, description = "需求单编号")
    private String code;
    // 需求单描述（模糊搜索）
    @ToolParam(required = false, description = "需求单描述")
    private String description;
    // 位置信息（模糊搜索）
    @ToolParam(required = false, description = "位置信息")
    private String location;
    // 提报用户姓名（模糊搜索）
    @ToolParam(required = false, description = "提报用户姓名")
    private String name;
    // 创建日期范围（格式如下：2025-05-01 ~ 2025-05-11）
    @ToolParam(required = false, description = "创建日期范围，格式如下：2025-05-01 ~ 2025-05-11")
    private String createdDate;
    /**
     * 需求单状态：
     * CREATE("已创建"),
     * PROCESS("处理中"),
     * FINISH("已完成"),
     * FOLLOWUP("已评价"),
     * CANCEL("已取消");
     */
    @ToolParam(required = false, description = "需求单状态，枚举值如下：CREATE（已创建）、PROCESS（处理中）、FINISH（已完成）、FOLLOWUP（已评价）、CANCEL（已取消）。")
    private List<String> status;
    // 当前操作用户ID（FM系统）
    @ToolParam(description = "当前操作用户ID")
    private Long userId;
    // 当前页码
    @ToolParam(description = "当前页码")
    private Integer pageNumber;
    // 每页显示条数
    @ToolParam(description = "每页显示条数")
    private Integer pageSize;
}