package cn.facilityone.ai.dto;

import cn.facilityone.ai.entity.AdminUserEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 后台管理员用户表
 */
public record AdminUserDTO(
    /*
      主键 (内部使用)
     */
        Long id,
    
    /*
      通用唯一识别码 (外部API使用)
     */
        String uuid,
    
    /*
      管理员用户名
     */
        String username,

    /*
      真实姓名
     */
        String realName,

    /*
      状态 (0:禁用, 1:启用)
     */
        Boolean status,

    /*
      最后登录时间
     */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        LocalDateTime lastLoginAt
) {
        public static AdminUserDTO of(AdminUserEntity adminUser) {
            return new AdminUserDTO(
                    adminUser.getId(),
                    adminUser.getUuid(),
                    adminUser.getUsername(),
                    adminUser.getRealName(),
                    adminUser.getStatus(),
                    adminUser.getLastLoginAt()
            );
        }
}
