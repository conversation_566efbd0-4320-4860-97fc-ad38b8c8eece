package cn.facilityone.ai.dto;

import cn.facilityone.ai.entity.MessageEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.ai.chat.messages.MessageType;

import java.time.LocalDateTime;

/**
 * 消息DTO
 */
@Data
public class MessageDTO {

    /**
     * 唯一标识符，自增主键
     */
    private Long id;

    /**
     * 关联到会话表
     */
    private String conversationId;

    /**
     * 消息在会话中的顺序（从1开始）
     */
    private Integer sequenceNumber;

    /**
     * 消息类型：user, assistant, system, tool
     */
    private MessageEntity.MessageType messageType;

    /**
     * 消息的文本内容
     */
    private String content;

    /**
     * 消息创建的时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

}
