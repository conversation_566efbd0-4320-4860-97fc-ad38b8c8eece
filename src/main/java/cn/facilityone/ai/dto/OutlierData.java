package cn.facilityone.ai.dto;

import lombok.Data;

/**
 * 离群数据传输对象
 * 用于封装检测到的离群工单信息
 */
@Data
public class OutlierData {

    /**
     * 工单数据
     */
    private WorkOrderData workOrder;

    /**
     * 离群类型
     */
    private OutlierType outlierType;

    /**
     * 离群程度（标准差倍数或其他度量）
     */
    private Double outlierScore;

    /**
     * 离群原因描述
     */
    private String reason;

    /**
     * 检测到的异常字段
     */
    private String anomalyField;

    /**
     * 异常值
     */
    private Object anomalyValue;

    /**
     * 正常值范围描述
     */
    private String normalRange;

    /**
     * 离群数据类型枚举
     */
    public enum OutlierType {
        /**
         * 处理时长异常
         */
        PROCESSING_TIME_OUTLIER("处理时长异常"),

        /**
         * 响应时间异常
         */
        RESPONSE_TIME_OUTLIER("响应时间异常"),

        /**
         * 业务规则异常
         */
        BUSINESS_RULE_OUTLIER("业务规则异常"),

        /**
         * 数据质量异常
         */
        DATA_QUALITY_OUTLIER("数据质量异常"),

        /**
         * 频率异常
         */
        FREQUENCY_OUTLIER("频率异常"),

        /**
         * 时间模式异常
         */
        TIME_PATTERN_OUTLIER("时间模式异常");

        private final String description;

        OutlierType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
