package cn.facilityone.ai.dto;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.List;

@Data
public class FmSearchWoOrderDTO {
    // 项目ID
    @ToolParam(required = true, description = "项目ID")
    private Long projectId;
    // 工单编号（模糊搜索）
    @ToolParam(required = false, description = "工单编号")
    private String code;
    // 工单描述（模糊搜索）
    @ToolParam(required = false, description = "工单描述")
    private String description;
    // 位置信息（模糊搜索）
    @ToolParam(required = false, description = "位置信息")
    private String location;
    // 优先级名称（模糊搜索）
    @ToolParam(required = false, description = "优先级名称")
    private String priorityName;
    // 服务类型名称（模糊搜索）
    @ToolParam(required = false, description = "服务类型名称")
    private String serviceTypeName;
    // 提报用户姓名（模糊搜索）
    @ToolParam(required = false, description = "提报用户姓名")
    private String name;
    // 创建日期范围（格式如下：2025-05-01 ~ 2025-05-11）
    @ToolParam(required = false, description = "创建日期范围，格式如下：2025-05-01 ~ 2025-05-11")
    private String createdDate;
    // 当前操作用户ID（FM系统）
    @ToolParam(required = true, description = "当前操作用户ID")
    private Long userId;
    // 当前页码
    @ToolParam(required = true, description = "当前页码")
    private Integer pageNumber;
    // 每页显示条数
    @ToolParam(required = true, description = "每页显示条数")
    private Integer pageSize;
}