-- 首先定义文件类型的 ENUM 类型
CREATE TYPE file_type_enum AS ENUM ('IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT', 'OTHER');

-- 定义一个通用的触发器函数，用于更新 updated_at 列
-- (PostgreSQL 没有 ON UPDATE CURRENT_TIMESTAMP 语法)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW(); -- 或 CURRENT_TIMESTAMP
   RETURN NEW;
END;
$$ language 'plpgsql';


-- 应用表
CREATE TABLE app (
  id BIGSERIAL PRIMARY KEY, -- 应用的唯一标识符 (主键，PostgreSQL 的 BIGSERIAL 相当于 BIGINT AUTO_INCREMENT)
  app_code VARCHAR(50) NOT NULL, -- 应用的编号，可以自定义
  app_name VARCHAR(255) NOT NULL, -- 应用名称
  description TEXT NULL, -- 应用描述 (NULL 是默认值，可以省略 NULL 关键字)
  status SMALLINT NOT NULL DEFAULT 1, -- 应用状态 (0:禁用, 1:启用) (TINYINT UNSIGNED 映射到 SMALLINT)
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 最后更新时间 (ON UPDATE 通过触发器实现)
  -- PRIMARY KEY (id) 由 BIGSERIAL PRIMARY KEY 完成
  -- MySQL 原文中的 UNIQUE KEY `idx_app_id` (`app_id`) 是一个逻辑错误，假设意图是 app_code 的唯一性
  CONSTRAINT uk_app_code UNIQUE (app_code) -- 为 app_code 添加唯一约束
);

-- 添加表和列的注释
COMMENT ON TABLE app IS '应用表';
COMMENT ON COLUMN app.id IS '应用的唯一标识符 (主键)';
COMMENT ON COLUMN app.app_code IS '应用的编号，可以自定义';
COMMENT ON COLUMN app.app_name IS '应用名称';
COMMENT ON COLUMN app.description IS '应用描述';
COMMENT ON COLUMN app.status IS '应用状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN app.created_at IS '创建时间';
COMMENT ON COLUMN app.updated_at IS '最后更新时间';

-- 为 app 表添加更新 updated_at 的触发器
CREATE TRIGGER update_app_updated_at
BEFORE UPDATE ON app
FOR EACH ROW
EXECUTE PROCEDURE update_updated_at_column();


-- 用户表
-- 注意: user 是 SQL 保留字，建议加双引号引用
CREATE TABLE "user" (
  id BIGSERIAL PRIMARY KEY, -- 用户的唯一数字ID (主键)
  user_uuid VARCHAR(36) NOT NULL, -- 用户的全局唯一标识符 (例如 UUID)
  username VARCHAR(100) NOT NULL, -- 用户标识符（外部）
  app_id BIGINT NOT NULL, -- 关联的应用表的 id (BIGINT UNSIGNED 映射到 BIGINT)
  user_status SMALLINT NOT NULL DEFAULT 1, -- 用户状态 (0:禁用, 1:启用) (TINYINT UNSIGNED 映射到 SMALLINT)
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 最后更新时间 (ON UPDATE 通过触发器实现)
  last_login_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL, -- 最后登录时间 (DEFAULT NULL 是默认值，可以省略)
  -- PRIMARY KEY (id) 由 BIGSERIAL PRIMARY KEY 完成
  CONSTRAINT uk_user_uuid UNIQUE (user_uuid), -- 用户的全局唯一标识符必须唯一
  CONSTRAINT uk_app_username UNIQUE (app_id, username) -- 同一个应用下的用户标识符必须唯一
  -- 可以选择添加外键约束: CONSTRAINT fk_user_app FOREIGN KEY (app_id) REFERENCES app(id)
);

-- 添加表和列的注释
COMMENT ON TABLE "user" IS '用户表';
COMMENT ON COLUMN "user".id IS '用户的唯一数字ID (主键)';
COMMENT ON COLUMN "user".user_uuid IS '用户的全局唯一标识符 (例如 UUID)';
COMMENT ON COLUMN "user".username IS '用户标识符（外部）';
COMMENT ON COLUMN "user".app_id IS '关联的应用表的 id';
COMMENT ON COLUMN "user".user_status IS '用户状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN "user".created_at IS '创建时间';
COMMENT ON COLUMN "user".updated_at IS '最后更新时间';
COMMENT ON COLUMN "user".last_login_at IS '最后登录时间';

-- 为 user 表添加更新 updated_at 的触发器
CREATE TRIGGER update_user_updated_at
BEFORE UPDATE ON "user"
FOR EACH ROW
EXECUTE PROCEDURE update_updated_at_column();

CREATE TABLE file (
    id BIGSERIAL PRIMARY KEY, -- 主键ID
    name VARCHAR(500), -- 文件名称
    path VARCHAR(500), -- 文件路径
    hash VARCHAR(300), -- 文件哈希值
    type VARCHAR(20) DEFAULT 'OTHER', -- 文件类型
    upload_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 文件上传时间
    is_thumb BOOLEAN DEFAULT FALSE, -- 是否为缩略图
    parent_file_id BIGINT, -- 父级文件ID(压缩后的图片等)
    app_id BIGINT NOT NULL, -- 关联的应用表的 id
    user_id BIGINT NOT NULL -- 关联的用户表的 id <-- 这是列表中的最后一个列定义
    -- PRIMARY KEY (id) 由 BIGSERIAL PRIMARY KEY 完成
    -- 可以选择添加外键约束:
    -- CONSTRAINT fk_file_app FOREIGN KEY (app_id) REFERENCES app(id),
    -- CONSTRAINT fk_file_user FOREIGN KEY (user_id) REFERENCES "user"(id),
    -- CONSTRAINT fk_file_parent FOREIGN KEY (parent_file_id) REFERENCES file(id)
); -- <-- 结束括号

-- 添加表和列的注释
COMMENT ON TABLE file IS '文件表';
COMMENT ON COLUMN file.id IS '主键ID';
COMMENT ON COLUMN file.name IS '文件名称';
COMMENT ON COLUMN file.path IS '文件路径';
COMMENT ON COLUMN file.hash IS '文件哈希值';
COMMENT ON COLUMN file.type IS '文件类型';
COMMENT ON COLUMN file.upload_time IS '文件上传时间';
COMMENT ON COLUMN file.is_thumb IS '是否为缩略图';
COMMENT ON COLUMN file.parent_file_id IS '父级文件ID(压缩后的图片等)';
COMMENT ON COLUMN file.app_id IS '关联的应用表的 id';
COMMENT ON COLUMN file.user_id IS '关联的用户表的 id';

-- 创建索引 (在 CREATE TABLE 之后)
CREATE INDEX idx_file_hash ON file (hash);
COMMENT ON INDEX idx_file_hash IS '文件哈希值索引';

CREATE INDEX idx_file_app_user ON file (app_id, user_id); -- 索引名稍微调整以包含表名
COMMENT ON INDEX idx_file_app_user IS '应用和用户联合索引';


-- 应用Token表
CREATE TABLE app_token (
  id BIGSERIAL PRIMARY KEY, -- Token的唯一标识符 (BIGINT UNSIGNED AUTO_INCREMENT 映射到 BIGSERIAL)
  app_id BIGINT NOT NULL, -- 关联的应用表的 id (BIGINT UNSIGNED 映射到 BIGINT)
  token VARCHAR(255) NOT NULL, -- 鉴权Token字符串
  description TEXT, -- token描述 (DEFAULT NULL 是默认值)
  expires_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL, -- Token过期时间 (NULL表示永不过期)
  status SMALLINT NOT NULL DEFAULT 1, -- Token状态 (0:失效, 1:有效) (TINYINT UNSIGNED 映射到 SMALLINT)
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 最后更新时间 (ON UPDATE 通过触发器实现) <-- ！！这里缺失了逗号！！
  -- PRIMARY KEY (id) 由 BIGSERIAL PRIMARY KEY 完成
  CONSTRAINT uk_app_token_token UNIQUE (token), -- Token字符串必须唯一 (索引名稍微调整以包含表名) <-- 即使上面有了逗号，这里作为列表中的元素，后面如果还有其他约束，也需要逗号
  -- 可以选择添加外键约束:
  CONSTRAINT fk_app_token_app FOREIGN KEY (app_id) REFERENCES app(id) ON DELETE CASCADE -- 外键关联 app 表，当 app 删除时，相关的 token 也删除
);

-- 添加表和列的注释
COMMENT ON TABLE app_token IS '应用Token表';
COMMENT ON COLUMN app_token.id IS 'Token的唯一标识符';
COMMENT ON COLUMN app_token.app_id IS '关联的应用表的 id';
COMMENT ON COLUMN app_token.token IS '鉴权Token字符串';
COMMENT ON COLUMN app_token.description IS 'token描述';
COMMENT ON COLUMN app_token.expires_at IS 'Token过期时间 (NULL表示永不过期)';
COMMENT ON COLUMN app_token.status IS 'Token状态 (0:失效, 1:有效)';
COMMENT ON COLUMN app_token.created_at IS '创建时间';
COMMENT ON COLUMN app_token.updated_at IS '最后更新时间';

-- 创建索引 (在 CREATE TABLE 之后)
CREATE INDEX idx_app_token_app_id ON app_token (app_id); -- 索引名稍微调整以包含表名
COMMENT ON INDEX idx_app_token_app_id IS 'app_id 索引';

-- 为 app_token 表添加更新 updated_at 的触发器
CREATE TRIGGER update_app_token_updated_at
BEFORE UPDATE ON app_token
FOR EACH ROW
EXECUTE PROCEDURE update_updated_at_column();


-- 用户与LLM会话关联表
CREATE TABLE user_conversation (
  id BIGSERIAL PRIMARY KEY, -- 唯一ID (主键) (BIGINT UNSIGNED AUTO_INCREMENT 映射到 BIGSERIAL)
  user_id BIGINT NOT NULL, -- 用户的唯一数字ID (外键, 关联 user.id) (BIGINT UNSIGNED 映射到 BIGINT)
  conversation_id VARCHAR(36) NOT NULL, -- LLM调用的会话ID
  title VARCHAR(255),
  tenant_id VARCHAR(64) NOT NULL DEFAULT 'default',
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间 (这里添加了逗号)
  -- PRIMARY KEY (id) 由 BIGSERIAL PRIMARY KEY 完成
  deleted bool DEFAULT FALSE NOT NULL,
  CONSTRAINT uk_user_conversation_id UNIQUE (conversation_id) -- 确保一个会话ID只关联一个用户 (索引名稍微调整以包含表名)
  -- 可以选择添加外键约束: CONSTRAINT fk_user_conversation_user FOREIGN KEY (user_id) REFERENCES "user"(id)
);

-- 添加表和列的注释
COMMENT ON TABLE user_conversation IS '用户与LLM会话关联表';
COMMENT ON COLUMN user_conversation.id IS '唯一ID (主键)';
COMMENT ON COLUMN user_conversation.user_id IS '用户的唯一数字ID (外键, 关联 user.id)';
COMMENT ON COLUMN user_conversation.conversation_id IS 'LLM调用的会话ID';
COMMENT ON COLUMN user_conversation.title IS '归纳的聊天标题';
COMMENT ON COLUMN user_conversation.tenant_id IS '租户ID';
COMMENT ON COLUMN user_conversation.created_at IS '创建时间';
COMMENT ON COLUMN user_conversation.deleted IS '删除标记,TRUE表示已删除，FALSE表示未删除';

-- 创建索引 (在 CREATE TABLE 之后)
CREATE INDEX idx_user_conversation_user_tenant ON user_conversation(user_id, tenant_id);
COMMENT ON INDEX idx_user_conversation_user_tenant IS '加速按用户及租户查询会话'; -- 添加注释


-- 创建 'messages' 表，用于存储会话中的每一条消息
CREATE TABLE IF NOT EXISTS messages (
    id BIGSERIAL PRIMARY KEY,     -- 唯一标识符，使用BIGSERIAL以防消息数量巨大
    conversation_id BIGINT NOT NULL, -- 外键，关联到 conversations 表的 id
    sequence_number INTEGER NOT NULL, -- 消息在会话中的顺序 (从1开始)
    message_type VARCHAR(50) NOT NULL,    -- 消息类型 (例如: 'user', 'assistant', 'system', 'tool')
    content TEXT NOT NULL,        -- 消息内容
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 消息创建时间
    metadata JSONB,               -- 可选：存储与消息相关的额外信息 (例如：模型名称、tokens用量、finish_reason等)
                                  -- JSONB 是 PostgreSQL 高效存储 JSON 的类型
    -- 确保同一个会话内的 sequence_number 是唯一的
    CONSTRAINT unique_conversation_sequence UNIQUE (conversation_id, sequence_number)
);
-- 为 conversation_id 添加索引，用于快速查询某个会话的所有消息
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id);
-- 为 conversation_id 和 sequence_number 添加联合索引，用于按顺序快速查询消息
CREATE INDEX IF NOT EXISTS idx_messages_conversation_sequence ON messages (conversation_id, sequence_number);

-- 为 'messages' 表的列添加注释
COMMENT ON COLUMN messages.conversation_id IS '关联到会话表';
COMMENT ON COLUMN messages.sequence_number IS '消息在会话中的顺序（从1开始）。';
COMMENT ON COLUMN messages.message_type IS '消息类型：user, assistant, system, tool';
COMMENT ON COLUMN messages.content IS '消息的文本内容';
COMMENT ON COLUMN messages.created_at IS '消息创建的时间戳';
COMMENT ON COLUMN messages.metadata IS '存储与消息相关的额外结构化数据（例如：模型信息、tokens 用量）的 JSONB 字段';

-- 创建 'app_vector_document' 表，用于存储应用、文件、向量文档关联信息
CREATE TABLE app_vector_document (
    id BIGSERIAL PRIMARY KEY,
    app_id BIGINT NOT NULL,
    file_id BIGINT NOT NULL,
    vector_id VARCHAR(150) NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (app_id, file_id, vector_id)
);
CREATE INDEX idx_avd_app_file_id ON app_vector_document(app_id, file_id);
CREATE INDEX idx_avd_vector_id ON app_vector_document(vector_id);

-- 创建应用配置表
CREATE TABLE app_configurations (
  id BIGSERIAL PRIMARY KEY,
  app_id BIGINT NOT NULL,
  tenant_id VARCHAR(64) NOT NULL DEFAULT 'default',
  config_group VARCHAR(128) NOT NULL DEFAULT 'default',
  config_key VARCHAR(255) NOT NULL,
  config_value TEXT NOT NULL,
  description VARCHAR(512) DEFAULT NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT uk_app_tenant_group_key UNIQUE (app_id, tenant_id, config_group, config_key)
);

-- 为表和列添加注释
COMMENT ON TABLE app_configurations IS '应用配置中心表';
COMMENT ON COLUMN app_configurations.id IS '主键ID (自增)';
COMMENT ON COLUMN app_configurations.app_id IS '所属应用的唯一数字标识符';
COMMENT ON COLUMN app_configurations.tenant_id IS '租户ID，用于数据隔离';
COMMENT ON COLUMN app_configurations.config_group IS '配置分组名';
COMMENT ON COLUMN app_configurations.config_key IS '配置项的Key';
COMMENT ON COLUMN app_configurations.config_value IS '配置项的Value，使用TEXT以支持较长的配置内容';
COMMENT ON COLUMN app_configurations.description IS '配置项的描述信息';
COMMENT ON COLUMN app_configurations.created_at IS '创建时间';
COMMENT ON COLUMN app_configurations.updated_at IS '最后更新时间';

-- 创建触发器，将其绑定到表上
CREATE TRIGGER update_app_configurations_updated_at
BEFORE UPDATE ON app_configurations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建后台管理员表
-- 启用 pgcrypto 扩展（如果尚未启用）
-- 该扩展用于生成 UUID。每个数据库只需要执行一次。
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE TABLE admin_user (
  id BIGSERIAL PRIMARY KEY,
  uuid UUID NOT NULL DEFAULT gen_random_uuid() UNIQUE,
  username VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  real_name VARCHAR(100),
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL
);
-- 在表上创建触发器，以自动更新 updated_at 字段
CREATE TRIGGER update_admin_user_updated_at
BEFORE UPDATE ON admin_user
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
-- 添加注释 (Comments)
COMMENT ON TABLE admin_user IS '后台管理员用户表';
COMMENT ON COLUMN admin_user.id IS '主键 (内部使用)';
COMMENT ON COLUMN admin_user.uuid IS '通用唯一识别码 (外部API使用)';
COMMENT ON COLUMN admin_user.username IS '管理员用户名';
COMMENT ON COLUMN admin_user.password IS '加密密码';
COMMENT ON COLUMN admin_user.real_name IS '真实姓名';
COMMENT ON COLUMN admin_user.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN admin_user.created_at IS '创建时间';
COMMENT ON COLUMN admin_user.updated_at IS '最后更新时间 (自动更新)';
COMMENT ON COLUMN admin_user.last_login_at IS '最后登录时间';
