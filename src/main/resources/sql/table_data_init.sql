-- 初始化 app_id 和 app_token
INSERT INTO app (id, app_code, app_name, description, status, created_at, updated_at) VALUES(1, 'fm-repair-wechat-h5', 'AI需求单-微信公众号-H5', '微信公众号AI提报需求', 1, '2025-05-23 14:48:30.000', '2025-06-06 14:51:49.000');
INSERT INTO app (id, app_code, app_name, description, status, created_at, updated_at) VALUES(2, 'fm-mix-fm-web', 'AI混合-FM-WEB', 'FM-WEB-工单&需求单&知识库', 1, '2025-06-05 10:54:05.000', '2025-06-12 11:56:27.488');
INSERT INTO app (id, app_code, app_name, description, status, created_at, updated_at) VALUES(3, 'fm-mix-coe-web', 'AI混合-COE-WEB', 'COE-WEB-工单&需求单&知识库', 1, '2025-06-05 16:36:41.000', '2025-06-12 11:56:27.505');
INSERT INTO app (id, app_code, app_name, description, status, created_at, updated_at) VALUES(4, 'fm-mix-fm-app', 'AI混合-FM-APP', 'FM-APP-工单&需求单&知识库', 1, '2025-06-12 11:54:35.875', '2025-06-12 11:56:27.511');
INSERT INTO app (id, app_code, app_name, description, status, created_at, updated_at) VALUES(5, 'fm-mix-coe-web-demo', 'AI混合-COE-WEB-演示环境', 'COE-WEB-多FM演示环境-外层-客服&路径跳转&报表分析', 1, '2025-07-23 16:48:40.242', '2025-08-05 15:06:28.182');

-- 初始化 app_token
INSERT INTO app_token (id, app_id, "token", description, expires_at, status, created_at, updated_at) VALUES(1, 1, 'd04ef318-2c46-41c4-9f08-e8d38c15bdb3', NULL, NULL, 1, '2025-05-23 14:49:04.000', '2025-05-23 14:49:04.000');
INSERT INTO app_token (id, app_id, "token", description, expires_at, status, created_at, updated_at) VALUES(2, 4, '66bd6a70-09a4-4957-aa46-8e510cdd917c', '测试使用', NULL, 1, '2025-06-09 11:46:55.000', '2025-06-16 15:39:26.970');
INSERT INTO app_token (id, app_id, "token", description, expires_at, status, created_at, updated_at) VALUES(3, 4, '70a56f53-59b7-4704-952e-b7733617f1da', 'APP调用使用', NULL, 1, '2025-06-16 15:39:10.868', '2025-06-16 15:39:10.868');
INSERT INTO app_token (id, app_id, "token", description, expires_at, status, created_at, updated_at) VALUES(5, 5, '366647fd-f586-4f3a-a9a1-24b0a166a5a8', '演示环境使用', NULL, 1, '2025-08-05 09:40:57.171', '2025-08-06 17:59:33.059');-- 更新序列

-- 更新序列
SELECT setval(pg_get_serial_sequence('app_token', 'id'), (SELECT MAX(id) FROM app_token));
SELECT setval(pg_get_serial_sequence('app', 'id'), (SELECT MAX(id) FROM app));