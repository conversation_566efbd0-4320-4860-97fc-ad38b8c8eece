<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--======================================= 本地变量 ======================================== -->
    <!-- 使用系统变量 LOG_DIR，若未设置则使用默认 logs 路径 -->
    <property name="logback.home" value="${LOG_DIR:-logs}"/>

    <!-- 应用名称：和统一配置中的项目代码保持一致（小写） -->
    <property name="APP_NAME" value="fone-ai" />
    <!--日志文件保留天数 -->
    <property name="LOG_MAX_HISTORY" value="7" />

    <conversionRule conversionWord="maskedMsg" converterClass="cn.facilityone.ai.config.log.Base64MaskingConverter" />

    <!--=========================== 控制台输出 ==================================== -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!-- 增强格式，包含类名、行号、MDC traceId -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}:%line - %maskedMsg%n</pattern>
        </encoder>
    </appender>

    <!--=========================== 日志文件输出（带大小滚动策略） ================================ -->
    <appender name="APP"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 按日期 + 分片编号命名，支持压缩 -->
            <FileNamePattern>${logback.home}/${APP_NAME}.%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <MaxHistory>${LOG_MAX_HISTORY}</MaxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{500}:%line - %maskedMsg%n</pattern>
        </encoder>
    </appender>

    <!--=============================== 异步日志支持（可选）====================================== -->
    <appender name="ASYNC_APP" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP"/>
        <queueSize>512</queueSize>
        <discardingThreshold>10</discardingThreshold>
    </appender>

    <!--=============================== 日志级别配置 ====================================== -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache.shiro" level="WARN"/>
    <logger name="freemarker" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.hibernate.SQL" level="DEBUG"/>
    <logger name="io.lettuce.core" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="org.mybatis.spring" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="INFO"/>
    <logger name="org.springframework.ai" level="DEBUG"/>
    <logger name="org.springframework.web" level="DEBUG"/>
    <logger name="reactor.netty" level="ERROR"/>
    <logger name="io.netty" level="ERROR"/>
    <logger name="com.github.victools.jsonschema.generator" level="WARN"/>
    <logger name="com.alibaba.dashscope" level="WARN"/>

    <!--=============================== 根日志器 ====================================== -->
    <root level="debug">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_APP"/>
    </root>
</configuration>
