🧠 你的任务 (Your Task)

根据用户当前输入 (`[inputText]`) 和历史对话 (`[historyMessages]`)，判断用户的**核心意图类别**，并从以下六个标签中选择一个最合适的作为输出：

> **H: 操作问题查询 (How-to Query)**
> **R: 需求提报 (Request Submission)**
> **F: 工单提报 (Fault / Ticket Submission)**
> **C: 需求查询 (Check Request)**
> **S: 工单查询 (Status of Ticket/Fault)**
> **O: 其它 (Other)**

---

### 🔍 判断优先级规则 (按顺序执行)

#### ✅ **第一优先级：显式关键词绝对优先 (Absolute Keyword Priority)**
此规则为最高指令。只要当前用户输入 `[inputText]` **同时包含**实体关键词和对应的意图关键词，**必须立即根据本规则进行分类**，无需考虑历史消息。

- 包含 **“需求”** + 提报意图（如“提、报、申请”） → **R**
- 包含 **“需求”** + 查询意图（如“查、看、进度”） → **C**
- 包含 **“工单”** + 提报意图（如“提、报、开”） → **F**
- 包含 **“工单”** + 查询意图（如“查、看、状态”） → **S**

> **[重要] 注意：** 如果 `[inputText]` 只包含实体词（如“需求”），但没有明确的意图动词（如“查”、“提”），则此规则不适用，**必须进入第二优先级判断**。

#### ✅ **第二优先级：上下文继承 (Context Inheritance)**
**当且仅当**第一优先级规则未触发时，此规则才生效。此规则包含两条核心指令：

1.  **回答追问（最高指令）**：当用户输入是**对上一轮系统提问的直接回答时**（如提供时间、编号、描述等信息），**绝对禁止**将其视为一个新任务。你**必须**继承并维持系统提问时已经建立的任务上下文 (R, F, C, S)。

2.  **话题延续**：如果历史消息已建立起一个明确的提报/查询上下文，且当前输入是在为**同一任务**提供补充信息，则继承该上下文分类。如果用户发起了新的、不同的话题，则不应继承。

#### ✅ **第三优先级：语义内容分析 (Semantic Content Analysis)**
若以上规则均未触发（即无关键词、无有效上下文），则基于用户输入的**核心语义**进行判断：

- **内容是“从无到有地申请”新资源/服务** → **R** (需求提报)
- **内容是“对已有的事物进行修复”或报告问题** → **F** (工单提报)
- 查询已提交的需求处理情况 → **C**
- 查询已提交的工单处理状态 → **S**
- 咨询操作方法、步骤 → **H**
- 闲聊、感谢、取消、无法判断等 → **O**

---

### **📌 标签定义精简说明**

| 标签 | 类别 | 关键特征 |
| :--- | :--- | :--- |
| H | 操作问题查询 | “怎么/如何/怎样” 做某事？ |
| R | 需求提报 | **申请/开通/新增** 资源或服务 (如账号、设备、权限) |
| F | 工单提报 | **修复/处理** 已有事物的故障或问题 (如漏水、报错、损坏) |
| C | 需求查询 | 查询**需求**的处理进度/结果 |
| S | 工单查询 | 查询**工单**的维修状态/进度 |
| O | 其他 | 无明确任务意图，如闲聊、问候、取消 |

---

### **🧪 示例场景 (全新优化版)**

**示例1：关键词绝对优先**
- **用户输入**: “帮我查一下工单进度，提报人李四，13877772222。”
- **历史消息**: (无)
- ➤ **分析**: 当前输入包含`工单`和`查``进度`，触发**第一优先级规则**。直接判定为 **S**。

**示例2：上下文继承 (提报场景)**
- **用户**: “我需要申请点东西。”
- **系统**: “好的，请问您想申请什么？”
- **用户**: “一个企业邮箱账号，给新同事张三。”
- ➤ **分析**: 第一句建立了“申请”意图(R)。第二句是在回答系统问题，补充申请的具体内容，触发**第二优先级规则**，继承分类 **R**。

**示例3：语义内容分析**
- **用户**: “我的办公室空调不制冷了。”
- ➤ **分析**: 无关键词、无上下文。内容核心是报告已有设备“空调”的“不制冷”故障，触发**第三优先级规则**，判定为 **F**。

**示例4：上下文继承 (查询场景 - 关键示例)**
- **用户**: “我想查需求进度”
- **系统**: “好的，请提供需求的编号或提交时间范围”
- **用户**: “就上周的”
- ➤ **分析**: 用户输入`就上周的`，本身无任何关键词。但它是对系统问题的直接回答，用于为“查需求”这个任务提供时间信息。**必须**触发**第二优先级规则（回答追问）**，继承上下文，判定为 **C**。

---

### **📤 输出要求**

请严格只输出以下之一的一个字母：

> **H, R, F, C, S, O**

---

### **💬 输入信息**

当前用户输入: {inputText}
历史消息: {historyMessages}

你的判断: