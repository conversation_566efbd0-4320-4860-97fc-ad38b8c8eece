🧠 你的任务 (Your Task)

根据用户当前输入 [`inputText`] 和历史对话 [`historyMessages`]，判断用户的核心意图，并从以下九个单字母标签中选择一个最合适的作为输出。输出必须是单个大写字母，且仅限以下集合：

> R 创建需求
> F 创建工单
> C 需求查询
> S 工单查询
> W 待处理工单查询
> Q 待处理需求查询
> I 巡检计划
> T 巡检任务查询
> O 其它问题

---

### 🔍 判断优先级规则（按顺序执行）

#### 1) 关键词绝对优先（实体 + 意图共现即判）
当 [`inputText`] 明确同时出现实体词与意图词时，直接分类，无需参考历史：
- 需求 + 提/报/申请/开通/新增 → R
- 工单 + 提/报/开/新建 → F
- 需求 + 查/看/进度/状态/编号/详情/记录/历史/范围 → C
- 工单 + 查/看/进度/状态/编号/详情/记录/历史/范围 → S
- 待处理/处理中/未完成 + 工单/维修/报修/单 + 查/看/进度/状态/编号/详情 → W
- 待处理/处理中/未完成 + 需求/申请/服务/单 + 查/看/进度/状态/编号/详情 → Q
- 巡检 + 计划/安排/周期/范围/排期 → I
- 巡检 + 任务 + 查/看/状态/进度/编号/结果/异常/记录 → T
- 不属于以上 → O

> 注意：如果只有实体词而无意图词，则进入规则2。

#### ✅ 第二优先级：上下文继承 Context Inheritance
当且仅当第一优先级规则未触发时，此规则才生效。包含两条核心指令：
1. 回答追问（最高指令）：当用户输入是对上一轮系统提问的直接回答时（如提供时间、编号、描述等信息），不得视为新任务，必须继承系统提问时已经建立的任务上下文（R, F, C, S, W, Q, T）。
2. 话题延续：历史已建立明确的提报/查询上下文且当前输入为同一任务提供补充信息，则继承该上下文；若发起新话题，则不继承。

#### 3) 语义内容分析（仅当规则1和2未触发）
- 从无到有的申请/开通/新增 → R
- 报告/修复现有问题/故障 → F
- 询问已提交“需求”的处理情况 → C
- 询问已提交“工单”的处理状态 → S
- 表达存在未完成事项，且包含查看/查询意图：工单类 → W；需求类 → Q
- 巡检计划层面（制定/安排/周期/范围） → I
- 巡检任务执行层面（任务状态/编号/结果/异常） → T
- 闲聊/感谢/取消/无法判断 → O

---

### 📌 标签与判定边界（精简）
- R 创建需求：申请/开通/新增服务或资源。
- F 创建工单：报修/维修/处理故障。
- C 需求查询：查询需求进度/状态/详情/编号/时间范围等。
- S 工单查询：查询工单进度/状态/详情/编号/时间范围等。
- W 待处理工单查询：用户明确希望查看未完成的工单状态/详情/编号等。
- Q 待处理需求查询：用户明确希望查看未完成的需求状态/详情/编号等。
- I 巡检计划：计划层面的制定/安排/查看。
- T 巡检任务查询：任务执行层面的状态/结果/异常/编号等。
- O 其它问题：非上述类别，包括泛化的操作方法咨询。

---

### 🧪 示例场景

示例1：关键词绝对优先（S）
- 输入：“查一下工单进度，提报人李四”
- 结果：S（工单 + 查/进度）

示例2：上下文继承（R）
- 对话：
  - 用户：“我想申请邮箱”
  - 系统：“请提供员工姓名与部门”
  - 用户：“张三，市场部”
- 结果：R（回答追问，继承既有“创建需求”）

示例3：语义分析（F）
- 输入：“办公室空调不制冷”
- 结果：F（报修故障）

示例4：待处理工单查询（W）
- 输入：“我有个报修还在处理中，查下现在的进度”
- 结果：W（待处理 + 工单语境 + 明确查询）

示例5：待处理需求查询（Q）
- 输入：“我还有个申请没走完流程，帮我看看到哪步了”
- 结果：Q（待处理 + 需求语境 + 明确查询）

示例6：巡检计划（I）
- 输入：“本周的巡检安排给我看下”
- 结果：I（巡检 + 计划/安排）

示例7：巡检任务查询（T）
- 输入：“巡检任务123的完成情况”
- 结果：T（巡检 + 任务 + 状态/编号）

示例8：需求查询上下文继承（C）
- 对话：
  - 用户：“我要查需求进度”
  - 系统：“请提供时间范围或编号”
  - 用户：“上周的”
- 结果：C（回答追问，继承）

---

### 📤 输出要求
请严格只输出以下之一的单个大写字母：
> R F C S O W Q I T

---

### 💬 输入信息
当前用户输入: {inputText}
历史消息: {historyMessages}

你的判断: